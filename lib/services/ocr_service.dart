import 'package:flutter/material.dart';
import 'package:google_ml_kit/google_ml_kit.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import 'dart:convert';

class OCRService extends ChangeNotifier {
  final ImagePicker _picker = ImagePicker();
  final TextRecognizer _textRecognizer = GoogleMlKit.vision.textRecognizer();

  Future<XFile?> pickImage(ImageSource source) async {
    try {
      final XFile? image = await _picker.pickImage(
        source: source,
        maxWidth: 1800,
        maxHeight: 1800,
        imageQuality: 85,
      );
      return image;
    } catch (e) {
      print('Error picking image: $e');
      return null;
    }
  }
  
  bool _isLoading = false;
  String _errorMessage = '';
  String _extractedText = '';

  bool get isLoading => _isLoading;
  String get errorMessage => _errorMessage;
  String get extractedText => _extractedText;

  // Pick image from camera
  Future<File?> pickImageFromCamera() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.camera,
        imageQuality: 80,
        maxWidth: 1024,
        maxHeight: 1024,
      );
      
      if (image != null) {
        return File(image.path);
      }
      return null;
    } catch (e) {
      _errorMessage = 'Failed to capture image: $e';
      notifyListeners();
      return null;
    }
  }

  // Pick image from gallery
  Future<File?> pickImageFromGallery() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 80,
        maxWidth: 1024,
        maxHeight: 1024,
      );
      
      if (image != null) {
        return File(image.path);
      }
      return null;
    } catch (e) {
      _errorMessage = 'Failed to pick image: $e';
      notifyListeners();
      return null;
    }
  }

  // Extract text from image using Google ML Kit
  Future<String?> extractTextFromImage(String imagePath) async {
    try {
      _isLoading = true;
      _errorMessage = '';
      notifyListeners();

      final InputImage inputImage = InputImage.fromFilePath(imagePath);
      final RecognizedText recognizedText = await _textRecognizer.processImage(inputImage);
      
      String extractedText = recognizedText.text;
      
      if (extractedText.trim().isEmpty) {
        _errorMessage = 'No text found in the image. Please try with a clearer image.';
        _isLoading = false;
        notifyListeners();
        return null;
      }
      
      _extractedText = extractedText;
      _isLoading = false;
      notifyListeners();
      
      return extractedText;
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'Failed to extract text: $e';
      notifyListeners();
      return null;
    }
  }

  // Convert image to base64 for API calls
  Future<String?> convertImageToBase64(File imageFile) async {
    try {
      List<int> imageBytes = await imageFile.readAsBytes();
      String base64Image = base64Encode(imageBytes);
      return base64Image;
    } catch (e) {
      _errorMessage = 'Failed to convert image: $e';
      notifyListeners();
      return null;
    }
  }

  // Process image with both ML Kit and prepare for AI analysis
  Future<Map<String, dynamic>?> processImageForAI(File imageFile) async {
    try {
      _isLoading = true;
      notifyListeners();

      // Extract text using ML Kit
      String? mlKitText = await extractTextFromImage(imageFile.path);
      
      // Convert to base64 for Gemini Vision API
      String? base64Image = await convertImageToBase64(imageFile);
      
      if (mlKitText != null || base64Image != null) {
        _isLoading = false;
        notifyListeners();
        
        return {
          'ml_kit_text': mlKitText,
          'base64_image': base64Image,
          'image_path': imageFile.path,
        };
      }
      
      _isLoading = false;
      notifyListeners();
      return null;
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'Failed to process image: $e';
      notifyListeners();
      return null;
    }
  }

  // Detect if image contains mathematical content
  bool containsMathematicalContent(String text) {
    // Common mathematical symbols and patterns
    List<String> mathPatterns = [
      r'\+', r'-', r'\*', r'/', r'=', r'\^', r'√', r'∫', r'∑',
      r'\d+\s*[+\-*/=]\s*\d+', // Basic arithmetic
      r'[a-zA-Z]\s*[+\-*/=]\s*[a-zA-Z0-9]', // Algebraic expressions
      r'\b(sin|cos|tan|log|ln)\b', // Mathematical functions
      r'\b(solve|find|calculate|equation|formula)\b', // Mathematical keywords
    ];
    
    for (String pattern in mathPatterns) {
      if (RegExp(pattern, caseSensitive: false).hasMatch(text)) {
        return true;
      }
    }
    
    return false;
  }

  // Detect subject based on keywords in text
  String detectSubject(String text) {
    Map<String, List<String>> subjectKeywords = {
      'Mathematics': [
        'equation', 'solve', 'calculate', 'formula', 'algebra', 'geometry',
        'trigonometry', 'calculus', 'derivative', 'integral', 'function',
        'graph', 'coordinate', 'angle', 'triangle', 'circle', 'square'
      ],
      'Physics': [
        'force', 'velocity', 'acceleration', 'energy', 'power', 'mass',
        'gravity', 'friction', 'momentum', 'wave', 'frequency', 'amplitude',
        'electric', 'magnetic', 'current', 'voltage', 'resistance', 'circuit'
      ],
      'Chemistry': [
        'element', 'compound', 'molecule', 'atom', 'ion', 'bond', 'reaction',
        'acid', 'base', 'salt', 'oxidation', 'reduction', 'catalyst',
        'periodic', 'valency', 'mole', 'molarity', 'pH'
      ],
      'Biology': [
        'cell', 'tissue', 'organ', 'organism', 'DNA', 'RNA', 'protein',
        'enzyme', 'photosynthesis', 'respiration', 'reproduction', 'evolution',
        'genetics', 'chromosome', 'gene', 'species', 'ecosystem'
      ],
      'English': [
        'grammar', 'sentence', 'paragraph', 'essay', 'poem', 'story',
        'character', 'plot', 'theme', 'metaphor', 'simile', 'alliteration',
        'noun', 'verb', 'adjective', 'adverb', 'tense'
      ],
      'History': [
        'ancient', 'medieval', 'modern', 'civilization', 'empire', 'dynasty',
        'war', 'battle', 'independence', 'freedom', 'movement', 'leader',
        'constitution', 'government', 'democracy'
      ],
      'Geography': [
        'continent', 'country', 'capital', 'river', 'mountain', 'ocean',
        'climate', 'weather', 'latitude', 'longitude', 'map', 'population',
        'agriculture', 'industry', 'resources'
      ]
    };
    
    String lowerText = text.toLowerCase();
    Map<String, int> subjectScores = {};
    
    subjectKeywords.forEach((subject, keywords) {
      int score = 0;
      for (String keyword in keywords) {
        if (lowerText.contains(keyword.toLowerCase())) {
          score++;
        }
      }
      subjectScores[subject] = score;
    });
    
    // Return subject with highest score
    String detectedSubject = 'General';
    int maxScore = 0;
    
    subjectScores.forEach((subject, score) {
      if (score > maxScore) {
        maxScore = score;
        detectedSubject = subject;
      }
    });
    
    return maxScore > 0 ? detectedSubject : 'General';
  }

  // Clean up extracted text
  String cleanExtractedText(String text) {
    return text
        .replaceAll(RegExp(r'\s+'), ' ')
        .replaceAll(RegExp(r'[^\w\s\d\+\-\*\/\=\(\)\[\]\{\}\.,;:!?]'), '')
        .trim();
  }

  String identifySubject(String text) {
    final lowerText = text.toLowerCase();
    
    // Math keywords
    if (lowerText.contains(RegExp(r'\b(equation|algebra|calculus|geometry|trigonometry|derivative|integral|matrix|vector|polynomial|quadratic|linear|logarithm|exponential|sine|cosine|tangent|theorem|proof|formula|\+|\-|\*|\/|\=|\(|\)|\^|√|∫|∑|∆|π|θ|α|β|γ|∞)\b'))) {
      return 'Mathematics';
    }
    
    // Physics keywords
    if (lowerText.contains(RegExp(r'\b(force|energy|momentum|velocity|acceleration|gravity|mass|weight|pressure|temperature|heat|light|wave|frequency|amplitude|electric|magnetic|current|voltage|resistance|power|work|kinetic|potential|newton|joule|watt|volt|ampere|ohm|pascal|kelvin|celsius|fahrenheit)\b'))) {
      return 'Physics';
    }
    
    // Chemistry keywords
    if (lowerText.contains(RegExp(r'\b(atom|molecule|element|compound|reaction|equation|bond|ionic|covalent|acid|base|salt|ph|oxidation|reduction|catalyst|enzyme|organic|inorganic|carbon|hydrogen|oxygen|nitrogen|periodic|table|mole|molarity|concentration|solution|solvent|solute)\b'))) {
      return 'Chemistry';
    }
    
    // Biology keywords
    if (lowerText.contains(RegExp(r'\b(cell|dna|rna|gene|chromosome|protein|enzyme|mitosis|meiosis|photosynthesis|respiration|evolution|natural|selection|ecosystem|biodiversity|organism|species|genus|family|order|class|phylum|kingdom|bacteria|virus|plant|animal|human|anatomy|physiology)\b'))) {
      return 'Biology';
    }
    
    // English keywords
    if (lowerText.contains(RegExp(r'\b(grammar|sentence|paragraph|essay|poem|poetry|literature|novel|story|character|plot|theme|metaphor|simile|alliteration|rhyme|rhythm|verb|noun|adjective|adverb|pronoun|preposition|conjunction|article)\b'))) {
      return 'English';
    }
    
    // History keywords
    if (lowerText.contains(RegExp(r'\b(war|battle|empire|kingdom|dynasty|civilization|ancient|medieval|modern|revolution|independence|constitution|democracy|monarchy|republic|treaty|alliance|colonization|exploration|discovery|invention)\b'))) {
      return 'History';
    }
    
    // Geography keywords
    if (lowerText.contains(RegExp(r'\b(continent|country|state|city|river|mountain|ocean|sea|lake|desert|forest|climate|weather|latitude|longitude|equator|hemisphere|tropics|arctic|antarctic|population|capital|border|map|atlas)\b'))) {
      return 'Geography';
    }
    
    return 'General';
  }

  void clearError() {
    _errorMessage = '';
    notifyListeners();
  }

  void clearExtractedText() {
    _extractedText = '';
    notifyListeners();
  }

  @override
  void dispose() {
    _textRecognizer.close();
    super.dispose();
  }
}