import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import '../models/quiz_model.dart';

class AIService extends ChangeNotifier {
  static const String _geminiApiKey = 'YOUR_GEMINI_API_KEY'; // Replace with actual API key
  static const String _geminiBaseUrl = 'https://generativelanguage.googleapis.com/v1beta/models';
  
  bool _isLoading = false;
  String _errorMessage = '';

  bool get isLoading => _isLoading;
  String get errorMessage => _errorMessage;

  // Solve question from extracted text
  Future<String?> solveQuestion(String extractedText, {String subject = ''}) async {
    try {
      _isLoading = true;
      notifyListeners();

      String prompt = '''
You are a subject expert teacher. Solve this question with steps and explain in simple student-friendly language:

$extractedText

Please provide:
1. A clear step-by-step solution
2. Easy-to-understand explanations
3. Key concepts involved
4. Tips to remember for similar questions

Start your response with an encouraging phrase like "Great question!" or "Excellent doubt!"''';

      String? response = await _callGeminiAPI(prompt);
      
      _isLoading = false;
      notifyListeners();
      return response;
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'Failed to solve question: $e';
      notifyListeners();
      return null;
    }
  }

  // Generate answer for voice question
  Future<String?> answerVoiceQuestion(String transcribedText) async {
    try {
      _isLoading = true;
      notifyListeners();

      String prompt = '''
Student asked: "$transcribedText". Give a short, clear and easy-to-understand spoken explanation like a school teacher.

Keep the response:
- Conversational and friendly
- Under 200 words
- Easy to understand when spoken aloud
- Include encouraging words

Start with praise like "That's a wonderful question!" or "Great thinking!"''';

      String? response = await _callGeminiAPI(prompt);
      
      _isLoading = false;
      notifyListeners();
      return response;
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'Failed to answer voice question: $e';
      notifyListeners();
      return null;
    }
  }

  // Smart tutor for class-wise subject questions
  Future<String?> getSmartTutorResponse(String question, int classNumber, String subject) async {
    try {
      _isLoading = true;
      notifyListeners();

      String prompt = '''
You are a CBSE Class $classNumber $subject teacher. Explain the following like you are teaching a student:

$question

Please:
- Use age-appropriate language for Class $classNumber students
- Include real-life examples where possible
- Break down complex concepts into simple parts
- Encourage the student with positive reinforcement

Start with an encouraging phrase and maintain a friendly teacher tone throughout.''';

      String? response = await _callGeminiAPI(prompt);
      
      _isLoading = false;
      notifyListeners();
      return response;
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'Failed to get tutor response: $e';
      notifyListeners();
      return null;
    }
  }

  // Generate notes for a topic
  Future<String?> generateNotes(String topic, int classNumber, String subject) async {
    try {
      _isLoading = true;
      notifyListeners();

      String prompt = '''
Give short notes and long notes for the topic "$topic" in Class $classNumber $subject.

Please provide:

**SHORT NOTES:**
- Key points in bullet format
- Important definitions
- Essential formulas (if applicable)

**DETAILED NOTES:**
- Comprehensive explanation
- Examples and illustrations
- Step-by-step processes
- Common mistakes to avoid

**SUMMARY (3 KEY POINTS):**
- 3 most important takeaways like a topper would remember

Make it student-friendly and easy to revise before exams.''';

      String? response = await _callGeminiAPI(prompt);
      
      _isLoading = false;
      notifyListeners();
      return response;
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'Failed to generate notes: $e';
      notifyListeners();
      return null;
    }
  }

  // Generate quiz questions
  Future<List<QuizQuestion>?> generateQuiz(int classNumber, String subject, String chapter) async {
    try {
      _isLoading = true;
      notifyListeners();

      String prompt = '''
Create 5 multiple-choice questions for Class $classNumber $subject Chapter "$chapter" with correct answer and explanation.

Format each question as JSON:
{
  "question": "Question text here",
  "options": ["Option A", "Option B", "Option C", "Option D"],
  "correct_answer": 0,
  "explanation": "Detailed explanation of why this is correct"
}

Provide exactly 5 questions in a JSON array format. Make questions:
- Age-appropriate for Class $classNumber
- Cover different difficulty levels
- Include conceptual and application-based questions
- Have clear, unambiguous options''';

      String? response = await _callGeminiAPI(prompt);
      
      if (response != null) {
        try {
          // Parse the JSON response
          List<dynamic> questionsJson = json.decode(response);
          List<QuizQuestion> questions = questionsJson
              .map((q) => QuizQuestion.fromMap(q))
              .toList();
          
          _isLoading = false;
          notifyListeners();
          return questions;
        } catch (parseError) {
          _errorMessage = 'Failed to parse quiz questions: $parseError';
        }
      }
      
      _isLoading = false;
      notifyListeners();
      return null;
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'Failed to generate quiz: $e';
      notifyListeners();
      return null;
    }
  }

  // Homework helper with practice questions
  Future<String?> getHomeworkHelp(String question) async {
    try {
      _isLoading = true;
      notifyListeners();

      String prompt = '''
Solve the question and then give 2 similar practice questions with answers.

Question: $question

Please provide:
1. **SOLUTION:** Step-by-step solution to the given question
2. **PRACTICE QUESTIONS:** 2 similar questions with complete solutions

Make sure the practice questions help reinforce the same concepts and are at a similar difficulty level.''';

      String? response = await _callGeminiAPI(prompt);
      
      _isLoading = false;
      notifyListeners();
      return response;
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'Failed to get homework help: $e';
      notifyListeners();
      return null;
    }
  }

  // Generate study suggestions based on progress
  Future<String?> getStudySuggestions(Map<String, dynamic> userProgress, int classNumber) async {
    try {
      _isLoading = true;
      notifyListeners();

      String progressText = userProgress.entries
          .map((e) => '${e.key}: completed')
          .join(', ');

      String prompt = '''
Based on this progress: $progressText, suggest what to study next and what to revise today for a Class $classNumber student.

Provide:
1. **NEXT TOPICS TO STUDY:** 3-4 recommended topics
2. **REVISION SCHEDULE:** What to revise today and this week
3. **STUDY TIPS:** Specific tips for better retention
4. **TIME MANAGEMENT:** Suggested time allocation

Keep suggestions practical and motivating.''';

      String? response = await _callGeminiAPI(prompt);
      
      _isLoading = false;
      notifyListeners();
      return response;
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'Failed to get study suggestions: $e';
      notifyListeners();
      return null;
    }
  }

  // Private method to call Gemini API
  Future<String?> _callGeminiAPI(String prompt) async {
    try {
      final url = Uri.parse('$_geminiBaseUrl/gemini-pro:generateContent?key=$_geminiApiKey');
      
      final response = await http.post(
        url,
        headers: {
          'Content-Type': 'application/json',
        },
        body: json.encode({
          'contents': [
            {
              'parts': [
                {'text': prompt}
              ]
            }
          ],
          'generationConfig': {
            'temperature': 0.7,
            'topK': 40,
            'topP': 0.95,
            'maxOutputTokens': 2048,
          }
        }),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['candidates'] != null && data['candidates'].isNotEmpty) {
          return data['candidates'][0]['content']['parts'][0]['text'];
        }
      } else {
        throw Exception('API request failed with status: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Network error: $e');
    }
    return null;
  }

  // Method to analyze image and extract text (for OCR integration)
  Future<String?> analyzeImageWithGemini(String base64Image) async {
    try {
      _isLoading = true;
      notifyListeners();

      final url = Uri.parse('$_geminiBaseUrl/gemini-pro-vision:generateContent?key=$_geminiApiKey');
      
      final response = await http.post(
        url,
        headers: {
          'Content-Type': 'application/json',
        },
        body: json.encode({
          'contents': [
            {
              'parts': [
                {
                  'text': 'Extract and transcribe all text from this image. If it contains mathematical equations, preserve the mathematical notation. If it\'s a question, identify the subject and provide the complete question text.'
                },
                {
                  'inline_data': {
                    'mime_type': 'image/jpeg',
                    'data': base64Image
                  }
                }
              ]
            }
          ],
        }),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['candidates'] != null && data['candidates'].isNotEmpty) {
          _isLoading = false;
          notifyListeners();
          return data['candidates'][0]['content']['parts'][0]['text'];
        }
      }
      
      _isLoading = false;
      notifyListeners();
      return null;
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'Failed to analyze image: $e';
      notifyListeners();
      return null;
    }
  }

  void clearError() {
    _errorMessage = '';
    notifyListeners();
  }

  Future<String> solveQuestionFromText(String text, String subject) async {
    try {
      final prompt = '''
You are an expert tutor for $subject. A student has asked you to solve this question:

"$text"

Please provide:
1. A clear, step-by-step solution
2. Explanation of key concepts used
3. Tips to remember for similar problems
4. Common mistakes to avoid

Make your response educational and easy to understand for a student.''';
      
      final response = await _callGeminiAPI(prompt);
      return response ?? 'Failed to get response';
    } catch (e) {
      throw Exception('Failed to solve question: $e');
    }
  }

  Future<String> getStudyPlanSuggestion(String userClass, Map<String, dynamic> progress) async {
    try {
      final prompt = '''
You are an AI study planner. Create a personalized weekly study plan for a Class $userClass student.

Current Progress: $progress

Please provide:
1. A balanced weekly schedule with all subjects
2. Recommended study hours per subject
3. Best times for different types of learning
4. Break and revision schedules
5. Tips for effective studying

Make the plan realistic and achievable for a student.''';
      
      final response = await _callGeminiAPI(prompt);
      return response ?? 'Failed to generate study plan';
    } catch (e) {
      throw Exception('Failed to get study plan: $e');
    }
  }
}