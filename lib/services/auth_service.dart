import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/user_model.dart';

class AuthService extends ChangeNotifier {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  User? _user;
  UserModel? _userModel;
  bool _isLoading = false;
  String _errorMessage = '';

  User? get user => _user;
  UserModel? get userModel => _userModel;
  bool get isLoading => _isLoading;
  String get errorMessage => _errorMessage;
  bool get isLoggedIn => _user != null;

  AuthService() {
    _auth.authStateChanges().listen((User? user) {
      _user = user;
      if (user != null) {
        _loadUserData();
      } else {
        _userModel = null;
      }
      notifyListeners();
    });
  }

  Future<void> _loadUserData() async {
    if (_user == null) return;
    
    try {
      DocumentSnapshot doc = await _firestore
          .collection('users')
          .doc(_user!.uid)
          .get();
      
      if (doc.exists) {
        _userModel = UserModel.fromMap(
          doc.data() as Map<String, dynamic>,
          doc.id,
        );
      }
    } catch (e) {
      _errorMessage = 'Failed to load user data: $e';
    }
    notifyListeners();
  }

  Future<bool> signInWithEmailAndPassword(String email, String password) async {
    try {
      _isLoading = true;
      _errorMessage = '';
      notifyListeners();

      UserCredential result = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
      
      _user = result.user;
      await _loadUserData();
      
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      _errorMessage = _getErrorMessage(e.toString());
      notifyListeners();
      return false;
    }
  }

  Future<bool> createUserWithEmailAndPassword(
    String email,
    String password,
    String name,
    int classNumber,
  ) async {
    try {
      _isLoading = true;
      _errorMessage = '';
      notifyListeners();

      UserCredential result = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );
      
      _user = result.user;
      
      // Create user document in Firestore
      if (_user != null) {
        UserModel newUser = UserModel(
          id: _user!.uid,
          name: name,
          email: email,
          classNumber: classNumber,
          progress: {},
          stars: 0,
          avatar: 'default',
          plan: 'Free',
          createdAt: DateTime.now(),
        );
        
        await _firestore
            .collection('users')
            .doc(_user!.uid)
            .set(newUser.toMap());
        
        _userModel = newUser;
      }
      
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      _errorMessage = _getErrorMessage(e.toString());
      notifyListeners();
      return false;
    }
  }

  Future<void> signOut() async {
    try {
      await _auth.signOut();
      _user = null;
      _userModel = null;
      _errorMessage = '';
      notifyListeners();
    } catch (e) {
      _errorMessage = 'Failed to sign out: $e';
      notifyListeners();
    }
  }

  Future<bool> resetPassword(String email) async {
    try {
      _isLoading = true;
      _errorMessage = '';
      notifyListeners();

      await _auth.sendPasswordResetEmail(email: email);
      
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      _errorMessage = _getErrorMessage(e.toString());
      notifyListeners();
      return false;
    }
  }

  Future<void> updateUserProfile({
    String? name,
    int? classNumber,
    String? avatar,
  }) async {
    if (_user == null || _userModel == null) return;
    
    try {
      Map<String, dynamic> updates = {};
      
      if (name != null) updates['name'] = name;
      if (classNumber != null) updates['class'] = classNumber;
      if (avatar != null) updates['avatar'] = avatar;
      
      await _firestore
          .collection('users')
          .doc(_user!.uid)
          .update(updates);
      
      _userModel = _userModel!.copyWith(
        name: name,
        classNumber: classNumber,
        avatar: avatar,
      );
      
      notifyListeners();
    } catch (e) {
      _errorMessage = 'Failed to update profile: $e';
      notifyListeners();
    }
  }

  Future<void> updateUserProgress(String subject, int points) async {
    if (_user == null || _userModel == null) return;
    
    try {
      Map<String, dynamic> progress = Map.from(_userModel!.progress);
      progress[subject] = (progress[subject] ?? 0) + points;
      
      await _firestore
          .collection('users')
          .doc(_user!.uid)
          .update({
        'progress': progress,
        'lastActive': FieldValue.serverTimestamp(),
      });
      
      _userModel = _userModel!.copyWith(progress: progress);
      notifyListeners();
    } catch (e) {
      _errorMessage = 'Failed to update progress: $e';
      notifyListeners();
    }
  }

  Future<void> updateUserStars(int stars) async {
    if (_user == null || _userModel == null) return;
    
    try {
      int newStars = _userModel!.stars + stars;
      
      await _firestore
          .collection('users')
          .doc(_user!.uid)
          .update({
        'stars': newStars,
        'lastActive': FieldValue.serverTimestamp(),
      });
      
      _userModel = _userModel!.copyWith(stars: newStars);
      notifyListeners();
    } catch (e) {
      _errorMessage = 'Failed to update stars: $e';
      notifyListeners();
    }
  }

  Future<void> addStars(int stars) async {
    if (_user == null || _userModel == null) return;
    
    try {
      int newStars = _userModel!.stars + stars;
      
      await _firestore
          .collection('users')
          .doc(_user!.uid)
          .update({'stars': newStars});
      
      _userModel = _userModel!.copyWith(stars: newStars);
      notifyListeners();
    } catch (e) {
      _errorMessage = 'Failed to add stars: $e';
      notifyListeners();
    }
  }

  String _getErrorMessage(String error) {
    if (error.contains('user-not-found')) {
      return 'No user found with this email.';
    } else if (error.contains('wrong-password')) {
      return 'Wrong password provided.';
    } else if (error.contains('email-already-in-use')) {
      return 'An account already exists with this email.';
    } else if (error.contains('weak-password')) {
      return 'Password is too weak.';
    } else if (error.contains('invalid-email')) {
      return 'Invalid email address.';
    } else {
      return 'An error occurred. Please try again.';
    }
  }

  void clearError() {
    _errorMessage = '';
    notifyListeners();
  }
}