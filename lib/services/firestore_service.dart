import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/quiz_model.dart';

class FirestoreService extends ChangeNotifier {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  bool _isLoading = false;
  String _errorMessage = '';

  bool get isLoading => _isLoading;
  String get errorMessage => _errorMessage;

  // Questions Collection Methods
  Future<String?> saveQuestion({
    required String question,
    required String subject,
    required String aiResponse,
    required String imageUrl,
    String? userId,
  }) async {
    try {
      _isLoading = true;
      notifyListeners();

      DocumentReference docRef = await _firestore.collection('questions').add({
        'userId': userId ?? '',
        'question': question,
        'image_url': imageUrl,
        'subject': subject,
        'AI_response': aiResponse,
        'created_at': DateTime.now().millisecondsSinceEpoch,
      });

      _isLoading = false;
      notifyListeners();
      return docRef.id;
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'Failed to save question: $e';
      notifyListeners();
      return null;
    }
  }

  Future<List<Question>> getUserQuestions(String userId) async {
    try {
      QuerySnapshot snapshot = await _firestore
          .collection('questions')
          .where('userId', isEqualTo: userId)
          .orderBy('created_at', descending: true)
          .get();

      return snapshot.docs
          .map((doc) => Question.fromMap(
                doc.data() as Map<String, dynamic>,
                doc.id,
              ))
          .toList();
    } catch (e) {
      _errorMessage = 'Failed to load questions: $e';
      notifyListeners();
      return [];
    }
  }

  // Notes Collection Methods
  Future<String?> saveNote({
    required String userId,
    required String topic,
    required int classNumber,
    required String subject,
    required String content,
    required String generatedBy,
  }) async {
    try {
      _isLoading = true;
      notifyListeners();

      DocumentReference docRef = await _firestore.collection('notes').add({
        'userId': userId,
        'topic': topic,
        'class': classNumber,
        'subject': subject,
        'content': content,
        'generated_by': generatedBy,
        'created_at': DateTime.now().millisecondsSinceEpoch,
      });

      _isLoading = false;
      notifyListeners();
      return docRef.id;
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'Failed to save note: $e';
      notifyListeners();
      return null;
    }
  }

  Future<List<Note>> getUserNotes(String userId) async {
    try {
      QuerySnapshot snapshot = await _firestore
          .collection('notes')
          .where('userId', isEqualTo: userId)
          .orderBy('created_at', descending: true)
          .get();

      return snapshot.docs
          .map((doc) => Note.fromMap(
                doc.data() as Map<String, dynamic>,
                doc.id,
              ))
          .toList();
    } catch (e) {
      _errorMessage = 'Failed to load notes: $e';
      notifyListeners();
      return [];
    }
  }

  Future<List<Note>> getNotesBySubject(String userId, String subject) async {
    try {
      QuerySnapshot snapshot = await _firestore
          .collection('notes')
          .where('userId', isEqualTo: userId)
          .where('subject', isEqualTo: subject)
          .orderBy('created_at', descending: true)
          .get();

      return snapshot.docs
          .map((doc) => Note.fromMap(
                doc.data() as Map<String, dynamic>,
                doc.id,
              ))
          .toList();
    } catch (e) {
      _errorMessage = 'Failed to load notes by subject: $e';
      notifyListeners();
      return [];
    }
  }

  // Quiz Collection Methods
  Future<String?> saveQuiz(QuizModel quiz) async {
    try {
      _isLoading = true;
      notifyListeners();

      DocumentReference docRef = await _firestore
          .collection('quizzes')
          .add(quiz.toMap());

      _isLoading = false;
      notifyListeners();
      return docRef.id;
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'Failed to save quiz: $e';
      notifyListeners();
      return null;
    }
  }

  Future<List<QuizModel>> getQuizzesByClass(int classNumber) async {
    try {
      QuerySnapshot snapshot = await _firestore
          .collection('quizzes')
          .where('class', isEqualTo: classNumber)
          .orderBy('created_at', descending: true)
          .get();

      return snapshot.docs
          .map((doc) => QuizModel.fromMap(
                doc.data() as Map<String, dynamic>,
                doc.id,
              ))
          .toList();
    } catch (e) {
      _errorMessage = 'Failed to load quizzes: $e';
      notifyListeners();
      return [];
    }
  }

  Future<QuizModel?> getQuizById(String quizId) async {
    try {
      DocumentSnapshot doc = await _firestore
          .collection('quizzes')
          .doc(quizId)
          .get();

      if (doc.exists) {
        return QuizModel.fromMap(
          doc.data() as Map<String, dynamic>,
          doc.id,
        );
      }
      return null;
    } catch (e) {
      _errorMessage = 'Failed to load quiz: $e';
      notifyListeners();
      return null;
    }
  }

  // Quiz Results Methods
  Future<String?> saveQuizResult(QuizResult result) async {
    try {
      _isLoading = true;
      notifyListeners();

      DocumentReference docRef = await _firestore
          .collection('quiz_results')
          .add(result.toMap());

      _isLoading = false;
      notifyListeners();
      return docRef.id;
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'Failed to save quiz result: $e';
      notifyListeners();
      return null;
    }
  }

  Future<List<QuizResult>> getUserQuizResults(String userId) async {
    try {
      QuerySnapshot snapshot = await _firestore
          .collection('quiz_results')
          .where('user_id', isEqualTo: userId)
          .orderBy('completed_at', descending: true)
          .get();

      return snapshot.docs
          .map((doc) => QuizResult.fromMap(
                doc.data() as Map<String, dynamic>,
              ))
          .toList();
    } catch (e) {
      _errorMessage = 'Failed to load quiz results: $e';
      notifyListeners();
      return [];
    }
  }

  // Voice Queries Methods
  Future<String?> saveVoiceQuery({
    required String userId,
    required String audioUrl,
    required String aiTranscript,
    required String aiAnswer,
  }) async {
    try {
      _isLoading = true;
      notifyListeners();

      DocumentReference docRef = await _firestore.collection('voice_queries').add({
        'userId': userId,
        'audio_url': audioUrl,
        'AI_transcript': aiTranscript,
        'AI_answer': aiAnswer,
        'created_at': DateTime.now().millisecondsSinceEpoch,
      });

      _isLoading = false;
      notifyListeners();
      return docRef.id;
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'Failed to save voice query: $e';
      notifyListeners();
      return null;
    }
  }

  // Schedule Methods
  Future<void> saveUserSchedule({
    required String userId,
    required Map<String, String> timetable,
    required List<String> revisionSuggestions,
  }) async {
    try {
      _isLoading = true;
      notifyListeners();

      await _firestore.collection('schedule').doc(userId).set({
        'timetable': timetable,
        'revision_suggestions': revisionSuggestions,
        'updated_at': DateTime.now().millisecondsSinceEpoch,
      });

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'Failed to save schedule: $e';
      notifyListeners();
    }
  }

  Future<Map<String, dynamic>?> getUserSchedule(String userId) async {
    try {
      DocumentSnapshot doc = await _firestore
          .collection('schedule')
          .doc(userId)
          .get();

      if (doc.exists) {
        return doc.data() as Map<String, dynamic>;
      }
      return null;
    } catch (e) {
      _errorMessage = 'Failed to load schedule: $e';
      notifyListeners();
      return null;
    }
  }

  // Leaderboard Methods
  Future<List<Map<String, dynamic>>> getLeaderboard({int limit = 10}) async {
    try {
      QuerySnapshot snapshot = await _firestore
          .collection('users')
          .orderBy('stars', descending: true)
          .limit(limit)
          .get();

      return snapshot.docs
          .map((doc) => {
                'id': doc.id,
                ...doc.data() as Map<String, dynamic>,
              })
          .toList();
    } catch (e) {
      _errorMessage = 'Failed to load leaderboard: $e';
      notifyListeners();
      return [];
    }
  }

  void clearError() {
    _errorMessage = '';
    notifyListeners();
  }
}