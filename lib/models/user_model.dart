class UserModel {
  final String id;
  final String name;
  final String email;
  final int classNumber;
  final Map<String, dynamic> progress;
  final int stars;
  final String avatar;
  final String plan; // Free or Pro
  final DateTime createdAt;

  UserModel({
    required this.id,
    required this.name,
    required this.email,
    required this.classNumber,
    required this.progress,
    required this.stars,
    required this.avatar,
    required this.plan,
    required this.createdAt,
  });

  factory UserModel.fromMap(Map<String, dynamic> map, String id) {
    return UserModel(
      id: id,
      name: map['name'] ?? '',
      email: map['email'] ?? '',
      classNumber: map['class'] ?? 7,
      progress: Map<String, dynamic>.from(map['progress'] ?? {}),
      stars: map['stars'] ?? 0,
      avatar: map['avatar'] ?? 'default',
      plan: map['plan'] ?? 'Free',
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at'] ?? 0),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'email': email,
      'class': classNumber,
      'progress': progress,
      'stars': stars,
      'avatar': avatar,
      'plan': plan,
      'created_at': createdAt.millisecondsSinceEpoch,
    };
  }

  UserModel copyWith({
    String? id,
    String? name,
    String? email,
    int? classNumber,
    Map<String, dynamic>? progress,
    int? stars,
    String? avatar,
    String? plan,
    DateTime? createdAt,
  }) {
    return UserModel(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      classNumber: classNumber ?? this.classNumber,
      progress: progress ?? this.progress,
      stars: stars ?? this.stars,
      avatar: avatar ?? this.avatar,
      plan: plan ?? this.plan,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}