class QuizModel {
  final String id;
  final int classNumber;
  final String chapter;
  final List<QuizQuestion> questions;
  final DateTime createdAt;

  QuizModel({
    required this.id,
    required this.classNumber,
    required this.chapter,
    required this.questions,
    required this.createdAt,
  });

  factory QuizModel.fromMap(Map<String, dynamic> map, String id) {
    return QuizModel(
      id: id,
      classNumber: map['class'] ?? 7,
      chapter: map['chapter'] ?? '',
      questions: (map['questions'] as List<dynamic>? ?? [])
          .map((q) => QuizQuestion.fromMap(q))
          .toList(),
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at'] ?? 0),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'class': classNumber,
      'chapter': chapter,
      'questions': questions.map((q) => q.toMap()).toList(),
      'created_at': createdAt.millisecondsSinceEpoch,
    };
  }
}

class QuizQuestion {
  final String question;
  final List<String> options;
  final int correctAnswer; // Index of correct option
  final String explanation;

  QuizQuestion({
    required this.question,
    required this.options,
    required this.correctAnswer,
    required this.explanation,
  });

  factory QuizQuestion.fromMap(Map<String, dynamic> map) {
    return QuizQuestion(
      question: map['question'] ?? '',
      options: List<String>.from(map['options'] ?? []),
      correctAnswer: map['correct_answer'] ?? 0,
      explanation: map['explanation'] ?? '',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'question': question,
      'options': options,
      'correct_answer': correctAnswer,
      'explanation': explanation,
    };
  }
}

class QuizResult {
  final String userId;
  final String quizId;
  final int score;
  final int totalQuestions;
  final List<int> userAnswers;
  final DateTime completedAt;

  QuizResult({
    required this.userId,
    required this.quizId,
    required this.score,
    required this.totalQuestions,
    required this.userAnswers,
    required this.completedAt,
  });

  factory QuizResult.fromMap(Map<String, dynamic> map) {
    return QuizResult(
      userId: map['user_id'] ?? '',
      quizId: map['quiz_id'] ?? '',
      score: map['score'] ?? 0,
      totalQuestions: map['total_questions'] ?? 0,
      userAnswers: List<int>.from(map['user_answers'] ?? []),
      completedAt: DateTime.fromMillisecondsSinceEpoch(map['completed_at'] ?? 0),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'user_id': userId,
      'quiz_id': quizId,
      'score': score,
      'total_questions': totalQuestions,
      'user_answers': userAnswers,
      'completed_at': completedAt.millisecondsSinceEpoch,
    };
  }

  double get percentage => (score / totalQuestions) * 100;
}

class Question {
  final String id;
  final String userId;
  final String imageUrl;
  final String subject;
  final String aiResponse;
  final DateTime createdAt;

  Question({
    required this.id,
    required this.userId,
    required this.imageUrl,
    required this.subject,
    required this.aiResponse,
    required this.createdAt,
  });

  factory Question.fromMap(Map<String, dynamic> map, String id) {
    return Question(
      id: id,
      userId: map['userId'] ?? '',
      imageUrl: map['image_url'] ?? '',
      subject: map['subject'] ?? '',
      aiResponse: map['AI_response'] ?? '',
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at'] ?? 0),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'image_url': imageUrl,
      'subject': subject,
      'AI_response': aiResponse,
      'created_at': createdAt.millisecondsSinceEpoch,
    };
  }
}

class Note {
  final String id;
  final String userId;
  final String topic;
  final int classNumber;
  final String subject;
  final String content;
  final String generatedBy; // AI or user
  final DateTime createdAt;

  Note({
    required this.id,
    required this.userId,
    required this.topic,
    required this.classNumber,
    required this.subject,
    required this.content,
    required this.generatedBy,
    required this.createdAt,
  });

  factory Note.fromMap(Map<String, dynamic> map, String id) {
    return Note(
      id: id,
      userId: map['userId'] ?? '',
      topic: map['topic'] ?? '',
      classNumber: map['class'] ?? 7,
      subject: map['subject'] ?? '',
      content: map['content'] ?? '',
      generatedBy: map['generated_by'] ?? 'user',
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at'] ?? 0),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'topic': topic,
      'class': classNumber,
      'subject': subject,
      'content': content,
      'generated_by': generatedBy,
      'created_at': createdAt.millisecondsSinceEpoch,
    };
  }
}