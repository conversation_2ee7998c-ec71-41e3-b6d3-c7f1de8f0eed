import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:provider/provider.dart';
import '../services/auth_service.dart';
import '../services/ai_service.dart';
import '../services/ocr_service.dart';
import '../services/firestore_service.dart';
import '../main.dart';

class DoubtUploadWidget extends StatefulWidget {
  const DoubtUploadWidget({super.key});

  @override
  State<DoubtUploadWidget> createState() => _DoubtUploadWidgetState();
}

class _DoubtUploadWidgetState extends State<DoubtUploadWidget> {
  XFile? _selectedImage;
  String _extractedText = '';
  String _aiResponse = '';
  bool _isProcessing = false;
  String _detectedSubject = '';

  Future<void> _pickImage(ImageSource source) async {
    final ocrService = Provider.of<OCRService>(context, listen: false);
    
    setState(() {
      _isProcessing = true;
      _extractedText = '';
      _aiResponse = '';
      _detectedSubject = '';
    });

    try {
      XFile? image = await ocrService.pickImage(source);
      if (image != null) {
        setState(() {
          _selectedImage = image;
        });
        
        // Extract text from image
        String? extractedText = await ocrService.extractTextFromImage(image.path);
        String detectedSubject = ocrService.identifySubject(extractedText ?? '');
        
        setState(() {
          _extractedText = extractedText ?? '';
          _detectedSubject = detectedSubject;
        });
        
        // Get AI response
        await _getAIResponse(extractedText ?? '', detectedSubject);
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error processing image: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isProcessing = false;
      });
    }
  }

  Future<void> _getAIResponse(String extractedText, String subject) async {
    if (extractedText.isEmpty) return;
    
    final aiService = Provider.of<AIService>(context, listen: false);
    final authService = Provider.of<AuthService>(context, listen: false);
    final firestoreService = Provider.of<FirestoreService>(context, listen: false);

    try {
      String response = await aiService.solveQuestionFromText(extractedText, subject);
      
      setState(() {
        _aiResponse = response;
      });
      
      // Save to Firestore
        if (authService.user != null && _selectedImage != null) {
          await firestoreService.saveQuestion(
            userId: authService.user!.uid,
            question: extractedText,
            subject: subject,
            aiResponse: response,
            imageUrl: _selectedImage!.path, // TODO: Upload to Firebase Storage
          );
        
        // Update user progress
        await authService.updateUserProgress(subject, 5);
        await authService.updateUserStars(5);
      }
    } catch (e) {
      setState(() {
        _aiResponse = 'Error: Failed to get AI response. Please try again.';
      });
    }
  }

  void _showImageSourceDialog() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Select Image Source',
              style: AppTextStyles.heading2,
            ),
            const SizedBox(height: 20),
            
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildSourceOption(
                  icon: Icons.camera_alt,
                  label: 'Camera',
                  onTap: () {
                    Navigator.pop(context);
                    _pickImage(ImageSource.camera);
                  },
                ),
                _buildSourceOption(
                  icon: Icons.photo_library,
                  label: 'Gallery',
                  onTap: () {
                    Navigator.pop(context);
                    _pickImage(ImageSource.gallery);
                  },
                ),
              ],
            ),
            
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildSourceOption({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 120,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: AppColors.lightBlue,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Column(
          children: [
            Icon(
              icon,
              size: 40,
              color: AppColors.darkBlue,
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: AppTextStyles.body1,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  const SizedBox(height: 20),
                  
                  // Upload Section
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      color: AppColors.lightGreen,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 10,
                          offset: const Offset(0, 5),
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                        const Text(
                          '📸 Upload Question Photo',
                          style: AppTextStyles.heading2,
                        ),
                        const SizedBox(height: 16),
                        
                        if (_selectedImage == null)
                          GestureDetector(
                            onTap: _showImageSourceDialog,
                            child: Container(
                              height: 200,
                              width: double.infinity,
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: AppColors.darkGreen,
                                  width: 2,
                                  style: BorderStyle.solid,
                                ),
                              ),
                              child: const Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.add_a_photo,
                                    size: 60,
                                    color: AppColors.darkGreen,
                                  ),
                                  SizedBox(height: 16),
                                  Text(
                                    'Tap to upload photo',
                                    style: AppTextStyles.body1,
                                  ),
                                  SizedBox(height: 8),
                                  Text(
                                    'Camera or Gallery',
                                    style: AppTextStyles.body2,
                                  ),
                                ],
                              ),
                            ),
                          )
                        else
                          Column(
                            children: [
                              ClipRRect(
                                borderRadius: BorderRadius.circular(12),
                                child: Image.file(
                                  File(_selectedImage!.path),
                                  height: 200,
                                  width: double.infinity,
                                  fit: BoxFit.cover,
                                ),
                              ),
                              const SizedBox(height: 16),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                children: [
                                  ElevatedButton.icon(
                                    onPressed: _showImageSourceDialog,
                                    icon: const Icon(Icons.refresh),
                                    label: const Text('Change'),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: AppColors.darkBlue,
                                      foregroundColor: Colors.white,
                                    ),
                                  ),
                                  if (_detectedSubject.isNotEmpty)
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 12,
                                        vertical: 8,
                                      ),
                                      decoration: BoxDecoration(
                                        color: AppColors.darkGreen,
                                        borderRadius: BorderRadius.circular(20),
                                      ),
                                      child: Text(
                                        _detectedSubject,
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ),
                                ],
                              ),
                            ],
                          ),
                      ],
                    ),
                  ),
                  
                  const SizedBox(height: 20),
                  
                  // Processing Indicator
                  if (_isProcessing)
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: AppColors.lightBlue,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Column(
                        children: [
                          CircularProgressIndicator(),
                          SizedBox(height: 16),
                          Text(
                            'Processing your question...',
                            style: AppTextStyles.body1,
                          ),
                          SizedBox(height: 8),
                          Text(
                            'This may take a few seconds',
                            style: AppTextStyles.body2,
                          ),
                        ],
                      ),
                    ),
                  
                  // Extracted Text
                  if (_extractedText.isNotEmpty && !_isProcessing)
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.grey[300]!),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Row(
                            children: [
                              Icon(
                                Icons.text_fields,
                                color: AppColors.darkBlue,
                              ),
                              SizedBox(width: 8),
                              Text(
                                'Extracted Text:',
                                style: AppTextStyles.heading3,
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Text(
                            _extractedText,
                            style: AppTextStyles.body2,
                          ),
                        ],
                      ),
                    ),
                  
                  const SizedBox(height: 16),
                  
                  // AI Response
                  if (_aiResponse.isNotEmpty)
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: AppColors.lightGreen,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: AppColors.darkGreen),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Row(
                            children: [
                              Icon(
                                Icons.smart_toy,
                                color: AppColors.darkGreen,
                              ),
                              SizedBox(width: 8),
                              Text(
                                'AI Teacher Solution:',
                                style: AppTextStyles.heading2,
                              ),
                            ],
                          ),
                          const SizedBox(height: 12),
                          Text(
                            _aiResponse,
                            style: AppTextStyles.body1,
                          ),
                          
                          const SizedBox(height: 16),
                          
                          // Action Buttons
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            children: [
                              ElevatedButton.icon(
                                onPressed: () {
                                  // TODO: Save to notes
                                },
                                icon: const Icon(Icons.bookmark_add),
                                label: const Text('Save'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: AppColors.darkGreen,
                                  foregroundColor: Colors.white,
                                ),
                              ),
                              ElevatedButton.icon(
                                onPressed: () {
                                  // TODO: Share functionality
                                },
                                icon: const Icon(Icons.share),
                                label: const Text('Share'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: AppColors.darkBlue,
                                  foregroundColor: Colors.white,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}