import 'package:flutter/material.dart';
import '../models/quiz_model.dart';
import '../main.dart';

class QuizCard extends StatelessWidget {
  final QuizQuestion question;
  final int selectedAnswer;
  final Function(int) onAnswerSelected;
  final bool showCorrectAnswer;
  final bool isReviewMode;

  const QuizCard({
    super.key,
    required this.question,
    required this.selectedAnswer,
    required this.onAnswerSelected,
    this.showCorrectAnswer = false,
    this.isReviewMode = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Question Text
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.lightBlue,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              question.question,
              style: AppTextStyles.heading3,
            ),
          ),
          
          const SizedBox(height: 20),
          
          // Answer Options
          Expanded(
            child: ListView.builder(
              itemCount: question.options.length,
              itemBuilder: (context, index) {
                return _buildAnswerOption(index);
              },
            ),
          ),
          
          // Explanation (shown in review mode or after answering)
          if (showCorrectAnswer && question.explanation.isNotEmpty)
            Container(
              width: double.infinity,
              margin: const EdgeInsets.only(top: 16),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.lightGreen,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: AppColors.darkGreen),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Row(
                    children: [
                      Icon(
                        Icons.lightbulb_outline,
                        color: AppColors.darkGreen,
                      ),
                      SizedBox(width: 8),
                      Text(
                        'Explanation:',
                        style: AppTextStyles.heading3,
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    question.explanation,
                    style: AppTextStyles.body1,
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildAnswerOption(int index) {
    final isSelected = selectedAnswer == index;
    final isCorrect = index == question.correctAnswer;
    final isWrong = showCorrectAnswer && isSelected && !isCorrect;
    
    Color backgroundColor;
    Color borderColor;
    Color textColor;
    IconData? icon;
    
    if (showCorrectAnswer) {
      if (isCorrect) {
        backgroundColor = Colors.green[100]!;
        borderColor = Colors.green;
        textColor = Colors.green[800]!;
        icon = Icons.check_circle;
      } else if (isWrong) {
        backgroundColor = Colors.red[100]!;
        borderColor = Colors.red;
        textColor = Colors.red[800]!;
        icon = Icons.cancel;
      } else {
        backgroundColor = Colors.grey[100]!;
        borderColor = Colors.grey[300]!;
        textColor = Colors.grey[700]!;
      }
    } else {
      if (isSelected) {
        backgroundColor = AppColors.lightBlue;
        borderColor = AppColors.darkBlue;
        textColor = AppColors.darkBlue;
        icon = Icons.radio_button_checked;
      } else {
        backgroundColor = Colors.grey[50]!;
        borderColor = Colors.grey[300]!;
        textColor = Colors.grey[800]!;
        icon = Icons.radio_button_unchecked;
      }
    }
    
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: isReviewMode ? null : () => onAnswerSelected(index),
          borderRadius: BorderRadius.circular(12),
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: backgroundColor,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: borderColor, width: 2),
            ),
            child: Row(
              children: [
                // Option Letter
                Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    color: borderColor,
                    shape: BoxShape.circle,
                  ),
                  child: Center(
                    child: Text(
                      String.fromCharCode(65 + index), // A, B, C, D
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ),
                ),
                
                const SizedBox(width: 16),
                
                // Option Text
                Expanded(
                  child: Text(
                    question.options[index],
                    style: AppTextStyles.body1.copyWith(
                      color: textColor,
                      fontWeight: isSelected || (showCorrectAnswer && isCorrect)
                          ? FontWeight.w600
                          : FontWeight.normal,
                    ),
                  ),
                ),
                
                // Status Icon
                if (icon != null)
                  Icon(
                    icon,
                    color: borderColor,
                    size: 24,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// Alternative Quiz Card for different question types
class ImageQuizCard extends StatelessWidget {
  final QuizQuestion question;
  final int selectedAnswer;
  final Function(int) onAnswerSelected;
  final String? imageUrl;
  final bool showCorrectAnswer;

  const ImageQuizCard({
    super.key,
    required this.question,
    required this.selectedAnswer,
    required this.onAnswerSelected,
    this.imageUrl,
    this.showCorrectAnswer = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Question Image (if available)
          if (imageUrl != null && imageUrl!.isNotEmpty)
            Container(
              width: double.infinity,
              height: 200,
              margin: const EdgeInsets.only(bottom: 16),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: Image.network(
                  imageUrl!,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      color: Colors.grey[200],
                      child: const Center(
                        child: Icon(
                          Icons.image_not_supported,
                          size: 50,
                          color: Colors.grey,
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
          
          // Question Text
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.lightGreen,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              question.question,
              style: AppTextStyles.heading3,
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Answer Options
          ...question.options.asMap().entries.map((entry) {
            final index = entry.key;
            final option = entry.value;
            final isSelected = selectedAnswer == index;
            final isCorrect = index == question.correctAnswer;
            final isWrong = showCorrectAnswer && isSelected && !isCorrect;
            
            Color backgroundColor;
            Color borderColor;
            
            if (showCorrectAnswer) {
              if (isCorrect) {
                backgroundColor = Colors.green[100]!;
                borderColor = Colors.green;
              } else if (isWrong) {
                backgroundColor = Colors.red[100]!;
                borderColor = Colors.red;
              } else {
                backgroundColor = Colors.grey[100]!;
                borderColor = Colors.grey[300]!;
              }
            } else {
              if (isSelected) {
                backgroundColor = AppColors.lightBlue;
                borderColor = AppColors.darkBlue;
              } else {
                backgroundColor = Colors.grey[50]!;
                borderColor = Colors.grey[300]!;
              }
            }
            
            return Container(
              margin: const EdgeInsets.only(bottom: 12),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: () => onAnswerSelected(index),
                  borderRadius: BorderRadius.circular(12),
                  child: Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: backgroundColor,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: borderColor, width: 2),
                    ),
                    child: Row(
                      children: [
                        Container(
                          width: 24,
                          height: 24,
                          decoration: BoxDecoration(
                            color: borderColor,
                            shape: BoxShape.circle,
                          ),
                          child: Center(
                            child: Text(
                              String.fromCharCode(65 + index),
                              style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 12,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            option,
                            style: AppTextStyles.body1.copyWith(
                              fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                            ),
                          ),
                        ),
                        if (showCorrectAnswer && isCorrect)
                          const Icon(
                            Icons.check_circle,
                            color: Colors.green,
                          )
                        else if (showCorrectAnswer && isWrong)
                          const Icon(
                            Icons.cancel,
                            color: Colors.red,
                          ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          }).toList(),
          
          // Explanation
          if (showCorrectAnswer && question.explanation.isNotEmpty)
            Container(
              width: double.infinity,
              margin: const EdgeInsets.only(top: 16),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.lightGreen,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: AppColors.darkGreen),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Row(
                    children: [
                      Icon(
                        Icons.lightbulb_outline,
                        color: AppColors.darkGreen,
                      ),
                      SizedBox(width: 8),
                      Text(
                        'Explanation:',
                        style: AppTextStyles.heading3,
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    question.explanation,
                    style: AppTextStyles.body1,
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }
}