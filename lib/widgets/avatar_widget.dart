import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/auth_service.dart';
import '../main.dart';

class AvatarWidget extends StatelessWidget {
  final double size;
  final bool showBorder;
  
  const AvatarWidget({
    super.key,
    this.size = 40,
    this.showBorder = false,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthService>(
      builder: (context, authService, _) {
        final user = authService.userModel;
        final initials = _getInitials(user?.name ?? 'Student');
        
        return Container(
          width: size,
          height: size,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: showBorder 
                ? Border.all(color: Colors.white, width: 2)
                : null,
            gradient: LinearGradient(
              colors: [AppColors.lightGreen, AppColors.darkGreen],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: Center(
            child: Text(
              initials,
              style: TextStyle(
                color: Colors.white,
                fontSize: size * 0.4,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        );
      },
    );
  }
  
  String _getInitials(String name) {
    final words = name.trim().split(' ');
    if (words.isEmpty) return 'S';
    if (words.length == 1) return words[0][0].toUpperCase();
    return '${words[0][0]}${words[1][0]}'.toUpperCase();
  }
}