import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/auth_service.dart';
import '../services/firestore_service.dart';
import '../models/user_model.dart';
import '../main.dart';

class ProgressScreen extends StatefulWidget {
  const ProgressScreen({super.key});

  @override
  State<ProgressScreen> createState() => _ProgressScreenState();
}

class _ProgressScreenState extends State<ProgressScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  
  Map<String, int> _subjectProgress = {};
  List<Map<String, dynamic>> _recentActivity = [];
  List<Map<String, dynamic>> _achievements = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadProgressData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadProgressData() async {
    setState(() {
      _isLoading = true;
    });

    final authService = Provider.of<AuthService>(context, listen: false);
    final firestoreService = Provider.of<FirestoreService>(context, listen: false);

    if (authService.user != null) {
      // Load subject progress (mock data for now)
      _subjectProgress = {
        'Mathematics': 75,
        'Science': 60,
        'English': 85,
        'Hindi': 45,
        'Social Science': 70,
        'Computer Science': 90,
      };

      // Load recent activity (mock data for now)
      _recentActivity = [
        {
          'type': 'quiz',
          'subject': 'Mathematics',
          'title': 'Algebra Quiz',
          'score': '8/10',
          'date': DateTime.now().subtract(const Duration(hours: 2)),
          'icon': Icons.quiz,
          'color': AppColors.darkGreen,
        },
        {
          'type': 'question',
          'subject': 'Science',
          'title': 'Photosynthesis Question',
          'score': 'Solved',
          'date': DateTime.now().subtract(const Duration(hours: 5)),
          'icon': Icons.help_outline,
          'color': AppColors.darkBlue,
        },
        {
          'type': 'notes',
          'subject': 'English',
          'title': 'Grammar Notes Generated',
          'score': 'Saved',
          'date': DateTime.now().subtract(const Duration(days: 1)),
          'icon': Icons.note_add,
          'color': Colors.orange,
        },
        {
          'type': 'quiz',
          'subject': 'Hindi',
          'title': 'व्याकरण Quiz',
          'score': '6/10',
          'date': DateTime.now().subtract(const Duration(days: 2)),
          'icon': Icons.quiz,
          'color': AppColors.darkGreen,
        },
      ];

      // Load achievements
      _achievements = [
        {
          'title': 'First Quiz Master',
          'description': 'Complete your first quiz',
          'icon': Icons.quiz,
          'earned': true,
          'date': DateTime.now().subtract(const Duration(days: 5)),
        },
        {
          'title': 'Question Solver',
          'description': 'Solve 10 questions using photo upload',
          'icon': Icons.camera_alt,
          'earned': true,
          'date': DateTime.now().subtract(const Duration(days: 3)),
        },
        {
          'title': 'Note Taker',
          'description': 'Generate 5 AI notes',
          'icon': Icons.note_add,
          'earned': true,
          'date': DateTime.now().subtract(const Duration(days: 1)),
        },
        {
          'title': 'Star Collector',
          'description': 'Earn 100 stars',
          'icon': Icons.star,
          'earned': false,
          'date': null,
        },
        {
          'title': 'Quiz Champion',
          'description': 'Score 90% or above in 5 quizzes',
          'icon': Icons.emoji_events,
          'earned': false,
          'date': null,
        },
        {
          'title': 'Study Streak',
          'description': 'Study for 7 consecutive days',
          'icon': Icons.local_fire_department,
          'earned': false,
          'date': null,
        },
      ];
    }

    setState(() {
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('My Progress'),
        backgroundColor: AppColors.lightGreen,
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.analytics), text: 'Overview'),
            Tab(icon: Icon(Icons.history), text: 'Activity'),
            Tab(icon: Icon(Icons.emoji_events), text: 'Achievements'),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildOverviewTab(),
                _buildActivityTab(),
                _buildAchievementsTab(),
              ],
            ),
    );
  }

  Widget _buildOverviewTab() {
    final authService = Provider.of<AuthService>(context);
    final user = authService.userModel;

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 20),
            
            // User Stats Card
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [AppColors.lightGreen, AppColors.lightBlue],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: Column(
                children: [
                  // Avatar and Name
                  Row(
                    children: [
                      CircleAvatar(
                        radius: 30,
                        backgroundColor: AppColors.darkGreen,
                        child: Text(
                          user?.name.substring(0, 1).toUpperCase() ?? 'U',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              user?.name ?? 'User',
                              style: AppTextStyles.heading2,
                            ),
                            Text(
                              'Class ${user?.classNumber ?? 'N/A'}',
                              style: AppTextStyles.body1,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 20),
                  
                  // Stats Row
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      _buildStatItem(
                        icon: Icons.star,
                        label: 'Stars',
                        value: '${user?.stars ?? 0}',
                        color: Colors.amber,
                      ),
                      _buildStatItem(
                        icon: Icons.trending_up,
                        label: 'Progress',
                        value: '${user?.progress ?? 0}',
                        color: AppColors.darkGreen,
                      ),
                      _buildStatItem(
                        icon: Icons.local_fire_department,
                        label: 'Streak',
                        value: '5', // Mock data
                        color: Colors.orange,
                      ),
                    ],
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Subject Progress
            const Text(
              'Subject Progress',
              style: AppTextStyles.heading2,
            ),
            const SizedBox(height: 16),
            
            ..._subjectProgress.entries.map((entry) {
              return _buildSubjectProgressCard(entry.key, entry.value);
            }).toList(),
            
            const SizedBox(height: 24),
            
            // Weekly Goal
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: AppColors.lightBlue,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Row(
                    children: [
                      Icon(
                        Icons.flag,
                        color: AppColors.darkBlue,
                      ),
                      SizedBox(width: 8),
                      Text(
                        'Weekly Goal',
                        style: AppTextStyles.heading3,
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  const Text(
                    'Complete 10 quizzes this week',
                    style: AppTextStyles.body1,
                  ),
                  const SizedBox(height: 12),
                  LinearProgressIndicator(
                    value: 0.7, // Mock progress
                    backgroundColor: Colors.white,
                    valueColor: const AlwaysStoppedAnimation<Color>(AppColors.darkBlue),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    '7/10 completed',
                    style: AppTextStyles.body2,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: color.withOpacity(0.2),
            shape: BoxShape.circle,
          ),
          child: Icon(
            icon,
            color: color,
            size: 24,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: AppTextStyles.heading3.copyWith(color: color),
        ),
        Text(
          label,
          style: AppTextStyles.body2,
        ),
      ],
    );
  }

  Widget _buildSubjectProgressCard(String subject, int progress) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: _getSubjectColor(subject),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              _getSubjectIcon(subject),
              color: Colors.white,
              size: 20,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  subject,
                  style: AppTextStyles.body1.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                LinearProgressIndicator(
                  value: progress / 100,
                  backgroundColor: Colors.grey[300],
                  valueColor: AlwaysStoppedAnimation<Color>(_getSubjectColor(subject)),
                ),
              ],
            ),
          ),
          const SizedBox(width: 16),
          Text(
            '$progress%',
            style: AppTextStyles.body1.copyWith(
              fontWeight: FontWeight.w600,
              color: _getSubjectColor(subject),
            ),
          ),
        ],
      ),
    );
  }

  Color _getSubjectColor(String subject) {
    switch (subject) {
      case 'Mathematics':
        return Colors.blue;
      case 'Science':
        return Colors.green;
      case 'English':
        return Colors.purple;
      case 'Hindi':
        return Colors.orange;
      case 'Social Science':
        return Colors.brown;
      case 'Computer Science':
        return Colors.teal;
      default:
        return AppColors.darkBlue;
    }
  }

  IconData _getSubjectIcon(String subject) {
    switch (subject) {
      case 'Mathematics':
        return Icons.calculate;
      case 'Science':
        return Icons.science;
      case 'English':
        return Icons.language;
      case 'Hindi':
        return Icons.translate;
      case 'Social Science':
        return Icons.public;
      case 'Computer Science':
        return Icons.computer;
      default:
        return Icons.book;
    }
  }

  Widget _buildActivityTab() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          if (_recentActivity.isEmpty)
            const Expanded(
              child: Center(
                child: Text(
                  'No recent activity',
                  style: AppTextStyles.heading2,
                ),
              ),
            )
          else
            Expanded(
              child: ListView.builder(
                itemCount: _recentActivity.length,
                itemBuilder: (context, index) {
                  final activity = _recentActivity[index];
                  return Container(
                    margin: const EdgeInsets.only(bottom: 12),
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 10,
                          offset: const Offset(0, 5),
                        ),
                      ],
                    ),
                    child: Row(
                      children: [
                        Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            color: activity['color'],
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            activity['icon'],
                            color: Colors.white,
                            size: 20,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                activity['title'],
                                style: AppTextStyles.body1.copyWith(
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Row(
                                children: [
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 8,
                                      vertical: 2,
                                    ),
                                    decoration: BoxDecoration(
                                      color: AppColors.lightGreen,
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: Text(
                                      activity['subject'],
                                      style: const TextStyle(
                                        fontSize: 12,
                                        color: AppColors.darkGreen,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    activity['score'],
                                    style: AppTextStyles.body2.copyWith(
                                      color: activity['color'],
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        Text(
                          _formatActivityDate(activity['date']),
                          style: AppTextStyles.body2.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildAchievementsTab() {
    final earnedAchievements = _achievements.where((a) => a['earned']).toList();
    final lockedAchievements = _achievements.where((a) => !a['earned']).toList();

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (earnedAchievements.isNotEmpty) ...[
              const Text(
                'Earned Achievements',
                style: AppTextStyles.heading2,
              ),
              const SizedBox(height: 16),
              ...earnedAchievements.map((achievement) {
                return _buildAchievementCard(achievement, true);
              }).toList(),
              const SizedBox(height: 24),
            ],
            
            if (lockedAchievements.isNotEmpty) ...[
              const Text(
                'Locked Achievements',
                style: AppTextStyles.heading2,
              ),
              const SizedBox(height: 16),
              ...lockedAchievements.map((achievement) {
                return _buildAchievementCard(achievement, false);
              }).toList(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildAchievementCard(Map<String, dynamic> achievement, bool earned) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: earned ? Colors.white : Colors.grey[100],
        borderRadius: BorderRadius.circular(12),
        border: earned
            ? Border.all(color: AppColors.darkGreen, width: 2)
            : Border.all(color: Colors.grey[300]!),
        boxShadow: earned
            ? [
                BoxShadow(
                  color: AppColors.darkGreen.withOpacity(0.2),
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ]
            : null,
      ),
      child: Row(
        children: [
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: earned ? AppColors.darkGreen : Colors.grey[400],
              shape: BoxShape.circle,
            ),
            child: Icon(
              achievement['icon'],
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  achievement['title'],
                  style: AppTextStyles.body1.copyWith(
                    fontWeight: FontWeight.w600,
                    color: earned ? Colors.black : Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  achievement['description'],
                  style: AppTextStyles.body2.copyWith(
                    color: earned ? Colors.grey[700] : Colors.grey[500],
                  ),
                ),
                if (earned && achievement['date'] != null)
                  Padding(
                    padding: const EdgeInsets.only(top: 4),
                    child: Text(
                      'Earned on ${_formatDate(achievement['date'])}',
                      style: AppTextStyles.body2.copyWith(
                        color: AppColors.darkGreen,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
              ],
            ),
          ),
          if (earned)
            const Icon(
              Icons.check_circle,
              color: AppColors.darkGreen,
              size: 24,
            )
          else
            Icon(
              Icons.lock,
              color: Colors.grey[400],
              size: 24,
            ),
        ],
      ),
    );
  }

  String _formatActivityDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      if (difference.inHours == 0) {
        return '${difference.inMinutes}m ago';
      }
      return '${difference.inHours}h ago';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else {
      return '${difference.inDays}d ago';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}