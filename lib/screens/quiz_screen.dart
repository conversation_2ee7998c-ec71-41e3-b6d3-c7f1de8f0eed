import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/auth_service.dart';
import '../services/ai_service.dart';
import '../services/firestore_service.dart';
import '../models/quiz_model.dart';
import '../main.dart';
import '../widgets/quiz_card.dart';

class QuizScreen extends StatefulWidget {
  const QuizScreen({super.key});

  @override
  State<QuizScreen> createState() => _QuizScreenState();
}

class _QuizScreenState extends State<QuizScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  
  String _selectedSubject = 'Mathematics';
  String _selectedChapter = '';
  int _selectedClass = 7;
  bool _isGeneratingQuiz = false;
  
  QuizModel? _currentQuiz;
  int _currentQuestionIndex = 0;
  List<int> _selectedAnswers = [];
  bool _isQuizCompleted = false;
  QuizResult? _quizResult;
  
  List<Map<String, dynamic>> _leaderboard = [];
  bool _isLoadingLeaderboard = false;
  
  final List<String> _subjects = [
    'Mathematics',
    'Science',
    'English',
    'Hindi',
    'Social Science',
    'Computer Science',
  ];
  
  final Map<String, List<String>> _chapters = {
    'Mathematics': [
      'Algebra',
      'Geometry',
      'Trigonometry',
      'Statistics',
      'Probability',
    ],
    'Science': [
      'Physics',
      'Chemistry',
      'Biology',
      'Environmental Science',
    ],
    'English': [
      'Grammar',
      'Literature',
      'Writing Skills',
      'Comprehension',
    ],
    'Hindi': [
      'व्याकरण',
      'साहित्य',
      'लेखन कौशल',
      'गद्य',
      'पद्य',
    ],
    'Social Science': [
      'History',
      'Geography',
      'Civics',
      'Economics',
    ],
    'Computer Science': [
      'Programming',
      'Data Structures',
      'Algorithms',
      'Database',
    ],
  };

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadUserClass();
    _selectedChapter = _chapters[_selectedSubject]?.first ?? '';
    _loadLeaderboard();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadUserClass() {
    final authService = Provider.of<AuthService>(context, listen: false);
    if (authService.userModel != null) {
      setState(() {
        _selectedClass = authService.userModel!.classNumber;
      });
    }
  }

  Future<void> _loadLeaderboard() async {
    setState(() {
      _isLoadingLeaderboard = true;
    });
    
    final firestoreService = Provider.of<FirestoreService>(context, listen: false);
    List<Map<String, dynamic>> leaderboard = await firestoreService.getLeaderboard();
    
    setState(() {
      _leaderboard = leaderboard;
      _isLoadingLeaderboard = false;
    });
  }

  Future<void> _generateQuiz() async {
    if (_selectedChapter.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select a chapter'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isGeneratingQuiz = true;
      _currentQuiz = null;
      _currentQuestionIndex = 0;
      _selectedAnswers = [];
      _isQuizCompleted = false;
      _quizResult = null;
    });

    final aiService = Provider.of<AIService>(context, listen: false);
    
    List<QuizQuestion>? questions = await aiService.generateQuiz(
      _selectedClass,
      _selectedSubject,
      _selectedChapter,
    );

    if (questions != null && questions.isNotEmpty) {
      final quiz = QuizModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        classNumber: _selectedClass,
        chapter: _selectedChapter,
        questions: questions,
        createdAt: DateTime.now(),
      );
      
      setState(() {
        _currentQuiz = quiz;
        _selectedAnswers = List.filled(questions.length, -1);
      });
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Failed to generate quiz. Please try again.'),
          backgroundColor: Colors.red,
        ),
      );
    }

    setState(() {
      _isGeneratingQuiz = false;
    });
  }

  void _selectAnswer(int answerIndex) {
    setState(() {
      _selectedAnswers[_currentQuestionIndex] = answerIndex;
    });
  }

  void _nextQuestion() {
    if (_currentQuestionIndex < _currentQuiz!.questions.length - 1) {
      setState(() {
        _currentQuestionIndex++;
      });
    } else {
      _completeQuiz();
    }
  }

  void _previousQuestion() {
    if (_currentQuestionIndex > 0) {
      setState(() {
        _currentQuestionIndex--;
      });
    }
  }

  Future<void> _completeQuiz() async {
    // Get services first
    final authService = Provider.of<AuthService>(context, listen: false);
    final firestoreService = Provider.of<FirestoreService>(context, listen: false);
    
    int correctAnswers = 0;
    for (int i = 0; i < _selectedAnswers.length; i++) {
      if (_selectedAnswers[i] == _currentQuiz!.questions[i].correctAnswer) {
        correctAnswers++;
      }
    }
    
    double percentage = (correctAnswers / _currentQuiz!.questions.length) * 100;
    int stars = _calculateStars(percentage);
    
    _quizResult = QuizResult(
      userId: authService.user?.uid ?? '',
      quizId: _currentQuiz!.id,
      score: correctAnswers,
      totalQuestions: _currentQuiz!.questions.length,
      userAnswers: _selectedAnswers,
      completedAt: DateTime.now(),
    );
    
    setState(() {
      _isQuizCompleted = true;
    });
    
    if (authService.user != null) {
      await firestoreService.saveQuizResult(_quizResult!);
      
      // Update user progress and stars
      await authService.updateUserProgress(_selectedSubject, 3);
      await authService.updateUserStars(stars);
      
      // Reload leaderboard
      await _loadLeaderboard();
    }
  }

  int _calculateStars(double percentage) {
    if (percentage >= 90) return 10;
    if (percentage >= 80) return 8;
    if (percentage >= 70) return 6;
    if (percentage >= 60) return 4;
    return 2;
  }

  void _resetQuiz() {
    setState(() {
      _currentQuiz = null;
      _currentQuestionIndex = 0;
      _selectedAnswers = [];
      _isQuizCompleted = false;
      _quizResult = null;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('AI Quiz Challenge'),
        backgroundColor: AppColors.lightGreen,
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.quiz), text: 'Take Quiz'),
            Tab(icon: Icon(Icons.history), text: 'Results'),
            Tab(icon: Icon(Icons.leaderboard), text: 'Leaderboard'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildQuizTab(),
          _buildResultsTab(),
          _buildLeaderboardTab(),
        ],
      ),
    );
  }

  Widget _buildQuizTab() {
    if (_currentQuiz == null) {
      return _buildQuizSetup();
    } else if (_isQuizCompleted) {
      return _buildQuizResults();
    } else {
      return _buildQuizQuestion();
    }
  }

  Widget _buildQuizSetup() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 20),
                  
                  // Quiz Setup Section
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: AppColors.lightBlue,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 10,
                          offset: const Offset(0, 5),
                        ),
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          '🧠 Create AI Quiz',
                          style: AppTextStyles.heading2,
                        ),
                        const SizedBox(height: 16),
                        
                        // Class Display
                        Row(
                          children: [
                            const Text(
                              'Class: ',
                              style: AppTextStyles.body1,
                            ),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 6,
                              ),
                              decoration: BoxDecoration(
                                color: AppColors.darkBlue,
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Text(
                                'Class $_selectedClass',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ],
                        ),
                        
                        const SizedBox(height: 16),
                        
                        // Subject Selection
                        const Text(
                          'Subject:',
                          style: AppTextStyles.body1,
                        ),
                        const SizedBox(height: 8),
                        DropdownButtonFormField<String>(
                          value: _selectedSubject,
                          decoration: InputDecoration(
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            filled: true,
                            fillColor: Colors.white,
                          ),
                          items: _subjects.map((subject) {
                            return DropdownMenuItem(
                              value: subject,
                              child: Text(subject),
                            );
                          }).toList(),
                          onChanged: (value) {
                            setState(() {
                              _selectedSubject = value!;
                              _selectedChapter = _chapters[_selectedSubject]?.first ?? '';
                            });
                          },
                        ),
                        
                        const SizedBox(height: 16),
                        
                        // Chapter Selection
                        const Text(
                          'Chapter/Topic:',
                          style: AppTextStyles.body1,
                        ),
                        const SizedBox(height: 8),
                        DropdownButtonFormField<String>(
                          value: _selectedChapter,
                          decoration: InputDecoration(
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            filled: true,
                            fillColor: Colors.white,
                          ),
                          items: (_chapters[_selectedSubject] ?? []).map((chapter) {
                            return DropdownMenuItem(
                              value: chapter,
                              child: Text(chapter),
                            );
                          }).toList(),
                          onChanged: (value) {
                            setState(() {
                              _selectedChapter = value!;
                            });
                          },
                        ),
                        
                        const SizedBox(height: 20),
                        
                        // Generate Quiz Button
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton(
                            onPressed: _isGeneratingQuiz ? null : _generateQuiz,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppColors.darkGreen,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                            ),
                            child: _isGeneratingQuiz
                                ? const Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      SizedBox(
                                        width: 20,
                                        height: 20,
                                        child: CircularProgressIndicator(
                                          color: Colors.white,
                                          strokeWidth: 2,
                                        ),
                                      ),
                                      SizedBox(width: 12),
                                      Text('Generating Quiz...'),
                                    ],
                                  )
                                : const Text(
                                    'Generate AI Quiz (5 Questions)',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  const SizedBox(height: 20),
                  
                  // Quiz Info
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: AppColors.lightGreen,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Column(
                      children: [
                        Icon(
                          Icons.info_outline,
                          color: AppColors.darkGreen,
                          size: 30,
                        ),
                        SizedBox(height: 8),
                        Text(
                          'Quiz Features',
                          style: AppTextStyles.heading3,
                        ),
                        SizedBox(height: 8),
                        Text(
                          '• 5 AI-generated questions\n• Multiple choice format\n• Instant explanations\n• Earn stars for performance\n• Compete on leaderboard',
                          style: AppTextStyles.body2,
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuizQuestion() {
    final question = _currentQuiz!.questions[_currentQuestionIndex];
    final progress = (_currentQuestionIndex + 1) / _currentQuiz!.questions.length;
    
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          // Progress Bar
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Question ${_currentQuestionIndex + 1} of ${_currentQuiz!.questions.length}',
                      style: AppTextStyles.body1,
                    ),
                    Text(
                      '${(progress * 100).toInt()}%',
                      style: AppTextStyles.body1,
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                LinearProgressIndicator(
                  value: progress,
                  backgroundColor: Colors.grey[300],
                  valueColor: const AlwaysStoppedAnimation<Color>(AppColors.darkGreen),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 20),
          
          // Question Card
          Expanded(
            child: QuizCard(
              question: question,
              selectedAnswer: _selectedAnswers[_currentQuestionIndex],
              onAnswerSelected: _selectAnswer,
            ),
          ),
          
          const SizedBox(height: 20),
          
          // Navigation Buttons
          Row(
            children: [
              if (_currentQuestionIndex > 0)
                Expanded(
                  child: ElevatedButton(
                    onPressed: _previousQuestion,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.grey[600],
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                    child: const Text('Previous'),
                  ),
                ),
              if (_currentQuestionIndex > 0) const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton(
                  onPressed: _selectedAnswers[_currentQuestionIndex] != -1
                      ? _nextQuestion
                      : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.darkGreen,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  child: Text(
                    _currentQuestionIndex == _currentQuiz!.questions.length - 1
                        ? 'Finish Quiz'
                        : 'Next',
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuizResults() {
    if (_quizResult == null) return const SizedBox();
    
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  const SizedBox(height: 20),
                  
                  // Results Header
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      color: _quizResult!.percentage >= 70
                          ? AppColors.lightGreen
                          : AppColors.lightBlue,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 10,
                          offset: const Offset(0, 5),
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                        Icon(
                          _quizResult!.percentage >= 70
                              ? Icons.celebration
                              : Icons.thumb_up,
                          size: 60,
                          color: _quizResult!.percentage >= 70
                              ? AppColors.darkGreen
                              : AppColors.darkBlue,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          _quizResult!.percentage >= 70
                              ? 'Excellent Work!'
                              : 'Good Effort!',
                          style: AppTextStyles.heading1,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'You scored ${_quizResult!.score}/${_quizResult!.totalQuestions}',
                          style: AppTextStyles.heading2,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '${_quizResult!.percentage.toInt()}%',
                          style: AppTextStyles.heading1.copyWith(
                            color: _quizResult!.percentage >= 70
                                ? AppColors.darkGreen
                                : AppColors.darkBlue,
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  const SizedBox(height: 20),
                  
                  // Action Buttons
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton(
                          onPressed: _resetQuiz,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.darkGreen,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                          ),
                          child: const Text('Take Another Quiz'),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () {
                            _tabController.animateTo(2); // Go to leaderboard
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.darkBlue,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                          ),
                          child: const Text('View Leaderboard'),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResultsTab() {
    return const Center(
      child: Text(
        'Quiz History\n(Coming Soon)',
        style: AppTextStyles.heading2,
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildLeaderboardTab() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          if (_isLoadingLeaderboard)
            const Expanded(
              child: Center(
                child: CircularProgressIndicator(),
              ),
            )
          else if (_leaderboard.isEmpty)
            const Expanded(
              child: Center(
                child: Text(
                  'No leaderboard data yet\nBe the first to take a quiz!',
                  style: AppTextStyles.heading2,
                  textAlign: TextAlign.center,
                ),
              ),
            )
          else
            Expanded(
              child: ListView.builder(
                itemCount: _leaderboard.length,
                itemBuilder: (context, index) {
                  final user = _leaderboard[index];
                  final isCurrentUser = Provider.of<AuthService>(context, listen: false)
                      .user?.uid == user['userId'];
                  
                  return Container(
                    margin: const EdgeInsets.only(bottom: 12),
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: isCurrentUser ? AppColors.lightGreen : Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      border: isCurrentUser
                          ? Border.all(color: AppColors.darkGreen, width: 2)
                          : null,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 10,
                          offset: const Offset(0, 5),
                        ),
                      ],
                    ),
                    child: Row(
                      children: [
                        // Rank
                        Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            color: index < 3
                                ? (index == 0 ? Colors.amber : index == 1 ? Colors.grey[400] : Colors.brown[300])
                                : AppColors.darkBlue,
                            shape: BoxShape.circle,
                          ),
                          child: Center(
                            child: Text(
                              '${index + 1}',
                              style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                        
                        const SizedBox(width: 16),
                        
                        // User Info
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                user['name'] ?? 'Unknown User',
                                style: AppTextStyles.body1.copyWith(
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              Text(
                                'Class ${user['class'] ?? 'N/A'}',
                                style: AppTextStyles.body2,
                              ),
                            ],
                          ),
                        ),
                        
                        // Stars
                        Row(
                          children: [
                            const Icon(
                              Icons.star,
                              color: Colors.amber,
                              size: 20,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              '${user['stars'] ?? 0}',
                              style: AppTextStyles.body1.copyWith(
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
        ],
      ),
    );
  }
}