import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/auth_service.dart';
import '../services/firestore_service.dart';
import '../services/ai_service.dart';
import '../main.dart';

class TimetableScreen extends StatefulWidget {
  const TimetableScreen({super.key});

  @override
  State<TimetableScreen> createState() => _TimetableScreenState();
}

class _TimetableScreenState extends State<TimetableScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  
  Map<String, List<Map<String, dynamic>>> _weeklySchedule = {};
  List<String> _subjects = [
    'Mathematics',
    'Science',
    'English',
    'Hindi',
    'Social Science',
    'Computer Science'
  ];
  
  List<String> _timeSlots = [
    '6:00 AM - 7:00 AM',
    '7:00 AM - 8:00 AM',
    '4:00 PM - 5:00 PM',
    '5:00 PM - 6:00 PM',
    '7:00 PM - 8:00 PM',
    '8:00 PM - 9:00 PM',
  ];
  
  List<String> _weekDays = [
    'Monday',
    'Tuesday',
    'Wednesday',
    'Thursday',
    'Friday',
    'Saturday',
    'Sunday'
  ];
  
  bool _isLoading = false;
  String _aiSuggestion = '';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _initializeSchedule();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _initializeSchedule() {
    // Initialize empty schedule
    for (String day in _weekDays) {
      _weeklySchedule[day] = [];
    }
    
    // Add some sample schedule items
    _weeklySchedule['Monday'] = [
      {
        'subject': 'Mathematics',
        'time': '6:00 AM - 7:00 AM',
        'topic': 'Algebra',
        'type': 'Study',
      },
      {
        'subject': 'Science',
        'time': '7:00 PM - 8:00 PM',
        'topic': 'Physics - Motion',
        'type': 'Revision',
      },
    ];
    
    _weeklySchedule['Tuesday'] = [
      {
        'subject': 'English',
        'time': '6:00 AM - 7:00 AM',
        'topic': 'Grammar',
        'type': 'Study',
      },
    ];
    
    _weeklySchedule['Wednesday'] = [
      {
        'subject': 'Hindi',
        'time': '5:00 PM - 6:00 PM',
        'topic': 'व्याकरण',
        'type': 'Practice',
      },
    ];
  }

  Future<void> _generateAITimetable() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final authService = Provider.of<AuthService>(context, listen: false);
      final aiService = Provider.of<AIService>(context, listen: false);
      
      if (authService.userModel != null) {
        final userClass = authService.userModel!.classNumber;
        final userProgress = authService.userModel!.progress;
        
        final prompt = '''
Create a personalized study timetable for a Class $userClass student.
Current progress: $userProgress
Available time slots: ${_timeSlots.join(', ')}
Subjects: ${_subjects.join(', ')}

Provide suggestions for:
1. Which subjects to focus on each day
2. Best time slots for different subjects
3. Balance between study, revision, and practice
4. Weekly goals and targets

Format the response as practical study tips and schedule recommendations.''';
        
        final response = await aiService.getStudyPlanSuggestion('10', {});
        
        setState(() {
          _aiSuggestion = response;
        });
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('AI study plan generated successfully!'),
              backgroundColor: AppColors.darkGreen,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error generating AI plan: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _addScheduleItem(String day) {
    showDialog(
      context: context,
      builder: (context) => _AddScheduleDialog(
        day: day,
        subjects: _subjects,
        timeSlots: _timeSlots,
        onAdd: (scheduleItem) {
          setState(() {
            _weeklySchedule[day]!.add(scheduleItem);
          });
        },
      ),
    );
  }

  void _removeScheduleItem(String day, int index) {
    setState(() {
      _weeklySchedule[day]!.removeAt(index);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('Study Timetable'),
        backgroundColor: AppColors.lightGreen,
        actions: [
          IconButton(
            icon: const Icon(Icons.auto_awesome),
            onPressed: _isLoading ? null : _generateAITimetable,
            tooltip: 'Generate AI Timetable',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.calendar_view_week), text: 'Weekly Schedule'),
            Tab(icon: Icon(Icons.lightbulb), text: 'AI Suggestions'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildWeeklyScheduleTab(),
          _buildAISuggestionsTab(),
        ],
      ),
    );
  }

  Widget _buildWeeklyScheduleTab() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          // Quick Stats
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [AppColors.lightBlue, AppColors.lightGreen],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildQuickStat(
                  icon: Icons.event,
                  label: 'This Week',
                  value: '${_getTotalScheduledHours()} hrs',
                ),
                _buildQuickStat(
                  icon: Icons.book,
                  label: 'Subjects',
                  value: '${_getScheduledSubjects().length}',
                ),
                _buildQuickStat(
                  icon: Icons.trending_up,
                  label: 'Progress',
                  value: '75%',
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 20),
          
          // Weekly Schedule
          Expanded(
            child: ListView.builder(
              itemCount: _weekDays.length,
              itemBuilder: (context, index) {
                final day = _weekDays[index];
                final scheduleItems = _weeklySchedule[day] ?? [];
                final isToday = _isToday(day);
                
                return Container(
                  margin: const EdgeInsets.only(bottom: 12),
                  decoration: BoxDecoration(
                    color: isToday ? AppColors.lightGreen : Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    border: isToday
                        ? Border.all(color: AppColors.darkGreen, width: 2)
                        : null,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 10,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  child: ExpansionTile(
                    title: Row(
                      children: [
                        Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            color: isToday ? AppColors.darkGreen : AppColors.darkBlue,
                            shape: BoxShape.circle,
                          ),
                          child: Center(
                            child: Text(
                              day.substring(0, 3).toUpperCase(),
                              style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 12,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                day,
                                style: AppTextStyles.body1.copyWith(
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              Text(
                                '${scheduleItems.length} sessions',
                                style: AppTextStyles.body2,
                              ),
                            ],
                          ),
                        ),
                        if (isToday)
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: AppColors.darkGreen,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: const Text(
                              'TODAY',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                      ],
                    ),
                    trailing: IconButton(
                      icon: const Icon(Icons.add),
                      onPressed: () => _addScheduleItem(day),
                    ),
                    children: [
                      if (scheduleItems.isEmpty)
                        const Padding(
                          padding: EdgeInsets.all(16.0),
                          child: Text(
                            'No sessions scheduled',
                            style: AppTextStyles.body2,
                          ),
                        )
                      else
                        ...scheduleItems.asMap().entries.map((entry) {
                          final index = entry.key;
                          final item = entry.value;
                          return _buildScheduleItem(day, index, item);
                        }).toList(),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickStat({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Column(
      children: [
        Icon(
          icon,
          color: AppColors.darkBlue,
          size: 24,
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: AppTextStyles.heading3.copyWith(
            color: AppColors.darkBlue,
          ),
        ),
        Text(
          label,
          style: AppTextStyles.body2,
        ),
      ],
    );
  }

  Widget _buildScheduleItem(String day, int index, Map<String, dynamic> item) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Row(
        children: [
          Container(
            width: 8,
            height: 40,
            decoration: BoxDecoration(
              color: _getSubjectColor(item['subject']),
              borderRadius: BorderRadius.circular(4),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      item['subject'],
                      style: AppTextStyles.body1.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: _getTypeColor(item['type']),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        item['type'],
                        style: const TextStyle(
                          fontSize: 10,
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  item['topic'],
                  style: AppTextStyles.body2,
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    const Icon(
                      Icons.access_time,
                      size: 14,
                      color: Colors.grey,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      item['time'],
                      style: AppTextStyles.body2.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          IconButton(
            icon: const Icon(
              Icons.delete_outline,
              color: Colors.red,
              size: 20,
            ),
            onPressed: () => _removeScheduleItem(day, index),
          ),
        ],
      ),
    );
  }

  Widget _buildAISuggestionsTab() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          // Generate Button
          if (_aiSuggestion.isEmpty)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: AppColors.lightBlue,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                children: [
                  const Icon(
                    Icons.auto_awesome,
                    size: 48,
                    color: AppColors.darkBlue,
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'Get AI-Powered Study Plan',
                    style: AppTextStyles.heading2,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'Let AI create a personalized study timetable based on your progress and goals.',
                    style: AppTextStyles.body1,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 20),
                  ElevatedButton(
                    onPressed: _isLoading ? null : _generateAITimetable,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.darkBlue,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 32,
                        vertical: 12,
                      ),
                    ),
                    child: _isLoading
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : const Text(
                            'Generate AI Plan',
                            style: TextStyle(color: Colors.white),
                          ),
                  ),
                ],
              ),
            )
          else
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const Icon(
                          Icons.lightbulb,
                          color: AppColors.darkGreen,
                        ),
                        const SizedBox(width: 8),
                        const Text(
                          'AI Study Recommendations',
                          style: AppTextStyles.heading2,
                        ),
                        const Spacer(),
                        IconButton(
                          icon: const Icon(Icons.refresh),
                          onPressed: _isLoading ? null : _generateAITimetable,
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 10,
                            offset: const Offset(0, 5),
                          ),
                        ],
                      ),
                      child: Text(
                        _aiSuggestion,
                        style: AppTextStyles.body1,
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  Color _getSubjectColor(String subject) {
    switch (subject) {
      case 'Mathematics':
        return Colors.blue;
      case 'Science':
        return Colors.green;
      case 'English':
        return Colors.purple;
      case 'Hindi':
        return Colors.orange;
      case 'Social Science':
        return Colors.brown;
      case 'Computer Science':
        return Colors.teal;
      default:
        return AppColors.darkBlue;
    }
  }

  Color _getTypeColor(String type) {
    switch (type) {
      case 'Study':
        return AppColors.darkGreen;
      case 'Revision':
        return AppColors.darkBlue;
      case 'Practice':
        return Colors.orange;
      case 'Test':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  bool _isToday(String day) {
    final today = DateTime.now().weekday;
    final dayIndex = _weekDays.indexOf(day) + 1;
    return today == dayIndex;
  }

  int _getTotalScheduledHours() {
    int total = 0;
    for (var daySchedule in _weeklySchedule.values) {
      total += daySchedule.length;
    }
    return total;
  }

  Set<String> _getScheduledSubjects() {
    Set<String> subjects = {};
    for (var daySchedule in _weeklySchedule.values) {
      for (var item in daySchedule) {
        subjects.add(item['subject']);
      }
    }
    return subjects;
  }
}

class _AddScheduleDialog extends StatefulWidget {
  final String day;
  final List<String> subjects;
  final List<String> timeSlots;
  final Function(Map<String, dynamic>) onAdd;

  const _AddScheduleDialog({
    required this.day,
    required this.subjects,
    required this.timeSlots,
    required this.onAdd,
  });

  @override
  State<_AddScheduleDialog> createState() => _AddScheduleDialogState();
}

class _AddScheduleDialogState extends State<_AddScheduleDialog> {
  String? _selectedSubject;
  String? _selectedTimeSlot;
  String? _selectedType;
  final _topicController = TextEditingController();
  
  final List<String> _types = ['Study', 'Revision', 'Practice', 'Test'];

  @override
  void dispose() {
    _topicController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('Add Session for ${widget.day}'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            DropdownButtonFormField<String>(
              decoration: const InputDecoration(
                labelText: 'Subject',
                border: OutlineInputBorder(),
              ),
              value: _selectedSubject,
              items: widget.subjects.map((subject) {
                return DropdownMenuItem(
                  value: subject,
                  child: Text(subject),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedSubject = value;
                });
              },
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              decoration: const InputDecoration(
                labelText: 'Time Slot',
                border: OutlineInputBorder(),
              ),
              value: _selectedTimeSlot,
              items: widget.timeSlots.map((timeSlot) {
                return DropdownMenuItem(
                  value: timeSlot,
                  child: Text(timeSlot),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedTimeSlot = value;
                });
              },
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              decoration: const InputDecoration(
                labelText: 'Type',
                border: OutlineInputBorder(),
              ),
              value: _selectedType,
              items: _types.map((type) {
                return DropdownMenuItem(
                  value: type,
                  child: Text(type),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedType = value;
                });
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _topicController,
              decoration: const InputDecoration(
                labelText: 'Topic/Chapter',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _canAdd() ? _addSession : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.darkGreen,
          ),
          child: const Text(
            'Add',
            style: TextStyle(color: Colors.white),
          ),
        ),
      ],
    );
  }

  bool _canAdd() {
    return _selectedSubject != null &&
        _selectedTimeSlot != null &&
        _selectedType != null &&
        _topicController.text.isNotEmpty;
  }

  void _addSession() {
    final scheduleItem = {
      'subject': _selectedSubject!,
      'time': _selectedTimeSlot!,
      'type': _selectedType!,
      'topic': _topicController.text,
    };
    
    widget.onAdd(scheduleItem);
    Navigator.of(context).pop();
  }
}