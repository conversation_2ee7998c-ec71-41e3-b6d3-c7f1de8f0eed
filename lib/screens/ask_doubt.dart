import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:speech_to_text/speech_to_text.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'dart:io';
import '../services/auth_service.dart';
import '../services/ai_service.dart';
import '../services/ocr_service.dart';
import '../services/firestore_service.dart';
import '../main.dart';
import '../widgets/doubt_upload.dart';

class AskDoubtScreen extends StatefulWidget {
  final int initialTab;
  
  const AskDoubtScreen({super.key, this.initialTab = 0});

  @override
  State<AskDoubtScreen> createState() => _AskDoubtScreenState();
}

class _AskDoubtScreenState extends State<AskDoubtScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _textController = TextEditingController();
  final SpeechToText _speechToText = SpeechToText();
  final FlutterTts _flutterTts = FlutterTts();
  
  bool _speechEnabled = false;
  bool _speechListening = false;
  String _speechText = '';
  String _aiResponse = '';
  bool _isProcessing = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _tabController.index = widget.initialTab;
    _initSpeech();
    _initTts();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _textController.dispose();
    super.dispose();
  }

  void _initSpeech() async {
    _speechEnabled = await _speechToText.initialize();
    setState(() {});
  }

  void _initTts() async {
    await _flutterTts.setLanguage('en-US');
    await _flutterTts.setSpeechRate(0.5);
    await _flutterTts.setVolume(1.0);
    await _flutterTts.setPitch(1.0);
  }

  void _startListening() async {
    await _speechToText.listen(
      onResult: (result) {
        setState(() {
          _speechText = result.recognizedWords;
        });
      },
    );
    setState(() {
      _speechListening = true;
    });
  }

  void _stopListening() async {
    await _speechToText.stop();
    setState(() {
      _speechListening = false;
    });
  }

  Future<void> _processVoiceQuestion() async {
    if (_speechText.isEmpty) return;
    
    setState(() {
      _isProcessing = true;
    });

    final aiService = Provider.of<AIService>(context, listen: false);
    final authService = Provider.of<AuthService>(context, listen: false);
    final firestoreService = Provider.of<FirestoreService>(context, listen: false);

    String? response = await aiService.answerVoiceQuestion(_speechText);
    
    if (response != null) {
      setState(() {
        _aiResponse = response;
      });
      
      // Save to Firestore
      if (authService.user != null) {
        await firestoreService.saveVoiceQuery(
          userId: authService.user!.uid,
          audioUrl: '', // TODO: Save actual audio file
          aiTranscript: _speechText,
          aiAnswer: response,
        );
      }
      
      // Speak the response
      await _flutterTts.speak(response);
    }
    
    setState(() {
      _isProcessing = false;
    });
  }

  Future<void> _processTextQuestion() async {
    if (_textController.text.trim().isEmpty) return;
    
    setState(() {
      _isProcessing = true;
    });

    final aiService = Provider.of<AIService>(context, listen: false);
    final authService = Provider.of<AuthService>(context, listen: false);
    
    String? response = await aiService.getSmartTutorResponse(
      _textController.text.trim(),
      authService.userModel?.classNumber ?? 7,
      'General',
    );
    
    if (response != null) {
      setState(() {
        _aiResponse = response;
      });
    }
    
    setState(() {
      _isProcessing = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('Ask Your Doubt'),
        backgroundColor: AppColors.lightGreen,
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.camera_alt), text: 'Photo'),
            Tab(icon: Icon(Icons.mic), text: 'Voice'),
            Tab(icon: Icon(Icons.text_fields), text: 'Text'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          // Photo Tab
          const DoubtUploadWidget(),
          
          // Voice Tab
          _buildVoiceTab(),
          
          // Text Tab
          _buildTextTab(),
        ],
      ),
    );
  }

  Widget _buildVoiceTab() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  const SizedBox(height: 20),
                  
                  // Voice Recording Section
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      color: AppColors.lightBlue,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 10,
                          offset: const Offset(0, 5),
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                        const Text(
                          '🎤 Voice Question',
                          style: AppTextStyles.heading2,
                        ),
                        const SizedBox(height: 16),
                        
                        // Microphone Button
                        GestureDetector(
                          onTap: _speechListening ? _stopListening : _startListening,
                          child: Container(
                            width: 100,
                            height: 100,
                            decoration: BoxDecoration(
                              color: _speechListening ? Colors.red : AppColors.darkBlue,
                              shape: BoxShape.circle,
                              boxShadow: [
                                BoxShadow(
                                  color: (_speechListening ? Colors.red : AppColors.darkBlue)
                                      .withOpacity(0.3),
                                  blurRadius: 20,
                                  spreadRadius: _speechListening ? 10 : 0,
                                ),
                              ],
                            ),
                            child: Icon(
                              _speechListening ? Icons.stop : Icons.mic,
                              size: 40,
                              color: Colors.white,
                            ),
                          ),
                        ),
                        
                        const SizedBox(height: 16),
                        Text(
                          _speechListening
                              ? 'Listening... Tap to stop'
                              : 'Tap to start recording',
                          style: AppTextStyles.body1,
                          textAlign: TextAlign.center,
                        ),
                        
                        if (!_speechEnabled)
                          const Text(
                            'Speech recognition not available',
                            style: TextStyle(color: Colors.red),
                          ),
                      ],
                    ),
                  ),
                  
                  const SizedBox(height: 20),
                  
                  // Transcribed Text
                  if (_speechText.isNotEmpty)
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.grey[300]!),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Your Question:',
                            style: AppTextStyles.body1,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            _speechText,
                            style: AppTextStyles.body2,
                          ),
                        ],
                      ),
                    ),
                  
                  const SizedBox(height: 16),
                  
                  // Process Button
                  if (_speechText.isNotEmpty && !_isProcessing)
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _processVoiceQuestion,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.darkGreen,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                        ),
                        child: const Text(
                          'Get Answer',
                          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                        ),
                      ),
                    ),
                  
                  // Loading Indicator
                  if (_isProcessing)
                    const Padding(
                      padding: EdgeInsets.all(20),
                      child: CircularProgressIndicator(),
                    ),
                  
                  // AI Response
                  if (_aiResponse.isNotEmpty)
                    Container(
                      width: double.infinity,
                      margin: const EdgeInsets.only(top: 16),
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: AppColors.lightGreen,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: AppColors.darkGreen),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(
                                Icons.smart_toy,
                                color: AppColors.darkGreen,
                              ),
                              const SizedBox(width: 8),
                              const Text(
                                'AI Teacher Says:',
                                style: AppTextStyles.heading2,
                              ),
                              const Spacer(),
                              IconButton(
                                icon: const Icon(Icons.volume_up),
                                onPressed: () => _flutterTts.speak(_aiResponse),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Text(
                            _aiResponse,
                            style: AppTextStyles.body1,
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTextTab() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  const SizedBox(height: 20),
                  
                  // Text Input Section
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: AppColors.lightGreen,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          '✍️ Type Your Question',
                          style: AppTextStyles.heading2,
                        ),
                        const SizedBox(height: 16),
                        
                        TextField(
                          controller: _textController,
                          maxLines: 5,
                          decoration: InputDecoration(
                            hintText: 'Type your question here...',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            filled: true,
                            fillColor: Colors.white,
                          ),
                        ),
                        
                        const SizedBox(height: 16),
                        
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton(
                            onPressed: _isProcessing ? null : _processTextQuestion,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppColors.darkGreen,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                            ),
                            child: _isProcessing
                                ? const CircularProgressIndicator(color: Colors.white)
                                : const Text(
                                    'Ask AI Teacher',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  // AI Response
                  if (_aiResponse.isNotEmpty)
                    Container(
                      width: double.infinity,
                      margin: const EdgeInsets.only(top: 20),
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 10,
                            offset: const Offset(0, 5),
                          ),
                        ],
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(
                                Icons.smart_toy,
                                color: AppColors.darkGreen,
                              ),
                              const SizedBox(width: 8),
                              const Text(
                                'AI Teacher Response:',
                                style: AppTextStyles.heading2,
                              ),
                            ],
                          ),
                          const SizedBox(height: 12),
                          Text(
                            _aiResponse,
                            style: AppTextStyles.body1,
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}