# AI Guru - Apka Study Sathi

A Flutter-based mobile application designed for students from Class 7 to 12, providing AI-powered study assistance with photo-based question solving, voice AI tutor, notes generation, quizzes, and personalized study planning.

## Features

### 🤖 AI-Powered Learning
- **Ask by Image**: Upload photos of questions and get instant AI-powered solutions
- **Voice Question & Answer**: Ask questions using voice and receive spoken responses
- **Smart Tutor**: Personalized AI tutoring with adaptive learning
- **Notes Generator**: AI-generated study notes for any topic
- **Quiz Mode**: Intelligent quiz generation with explanations

### 📚 Study Management
- **Personal Study Planner**: AI-suggested timetables and study schedules
- **Progress Tracking**: Monitor learning progress across subjects
- **Achievement System**: Earn stars and unlock achievements
- **Subject Management**: Organize studies by subjects (Math, Science, English, Hindi, Social Science, Computer Science)

### 🎯 User Experience
- **Multi-language Support**: English and Hindi
- **Offline Capability**: Core features work without internet
- **Beautiful UI**: Modern, intuitive design with smooth animations
- **Dark Mode**: Eye-friendly dark theme option

## Tech Stack

### Frontend
- **Flutter**: Cross-platform mobile development
- **Provider**: State management
- **Material Design**: UI components and theming

### Backend & Services
- **Firebase Auth**: User authentication and management
- **Cloud Firestore**: Real-time database for user data
- **Firebase Storage**: File and image storage
- **Firebase Cloud Messaging**: Push notifications
- **Firebase Hosting**: Web deployment

### AI & ML
- **Gemini API**: AI responses and question solving
- **Google ML Kit**: Text recognition and OCR
- **Speech-to-Text**: Voice input processing
- **Text-to-Speech**: Voice output generation

### Additional Libraries
- **Image Picker**: Camera and gallery access
- **HTTP**: API communication
- **Shared Preferences**: Local data storage
- **Lottie**: Smooth animations
- **UUID**: Unique identifier generation

## Project Structure

```
lib/
├── main.dart                 # App entry point and configuration
├── models/                   # Data models
│   ├── user_model.dart      # User data structure
│   └── quiz_model.dart      # Quiz and question models
├── services/                # Business logic and API services
│   ├── auth_service.dart    # Authentication management
│   ├── firestore_service.dart # Database operations
│   ├── ai_service.dart      # AI API integration
│   └── ocr_service.dart     # Image text extraction
├── screens/                 # UI screens
│   ├── auth/               # Authentication screens
│   │   ├── login_screen.dart
│   │   ├── register_screen.dart
│   │   └── forgot_password_screen.dart
│   ├── home.dart           # Main dashboard
│   ├── ask_doubt.dart      # Question asking interface
│   ├── notes_screen.dart   # Notes management
│   ├── quiz_screen.dart    # Quiz interface
│   ├── progress_screen.dart # Progress tracking
│   ├── timetable_screen.dart # Study planning
│   └── settings_screen.dart # App settings
└── widgets/                # Reusable UI components
    ├── doubt_upload.dart   # Image upload widget
    └── quiz_card.dart      # Quiz question display
```

## Database Structure

### Users Collection
```javascript
{
  userId: string,
  name: string,
  email: string,
  class: number,
  progress: number,
  stars: number,
  avatar: string,
  plan: 'free' | 'pro',
  createdAt: timestamp
}
```

### Questions Collection
```javascript
{
  questionId: string,
  userId: string,
  imageUrl: string,
  extractedText: string,
  aiResponse: string,
  subject: string,
  difficulty: string,
  createdAt: timestamp
}
```

### Quizzes Collection
```javascript
{
  quizId: string,
  userId: string,
  subject: string,
  questions: array,
  score: number,
  totalQuestions: number,
  completedAt: timestamp
}
```

## Setup Instructions

### Prerequisites
- Flutter SDK (3.0 or higher)
- Dart SDK
- Android Studio / VS Code
- Firebase account
- Gemini API key

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd ai-study-app
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Firebase Setup**
   - Create a new Firebase project
   - Enable Authentication (Email/Password)
   - Create Firestore database
   - Enable Storage
   - Download `google-services.json` (Android) and `GoogleService-Info.plist` (iOS)
   - Place configuration files in respective platform folders

4. **API Configuration**
   - Get Gemini API key from Google AI Studio
   - Add API key to your environment or configuration

5. **Run the app**
   ```bash
   flutter run
   ```

### Firebase Configuration

1. **Authentication**
   - Enable Email/Password sign-in method
   - Configure password reset templates

2. **Firestore Rules**
   ```javascript
   rules_version = '2';
   service cloud.firestore {
     match /databases/{database}/documents {
       match /users/{userId} {
         allow read, write: if request.auth != null && request.auth.uid == userId;
       }
       match /questions/{questionId} {
         allow read, write: if request.auth != null && request.auth.uid == resource.data.userId;
       }
       match /quizzes/{quizId} {
         allow read, write: if request.auth != null && request.auth.uid == resource.data.userId;
       }
     }
   }
   ```

3. **Storage Rules**
   ```javascript
   rules_version = '2';
   service firebase.storage {
     match /b/{bucket}/o {
       match /questions/{userId}/{allPaths=**} {
         allow read, write: if request.auth != null && request.auth.uid == userId;
       }
     }
   }
   ```

## Environment Variables

Create a `.env` file in the root directory:
```
GEMINI_API_KEY=your_gemini_api_key_here
FIREBASE_PROJECT_ID=your_firebase_project_id
```

## Build Instructions

### Android
```bash
flutter build apk --release
# or for app bundle
flutter build appbundle --release
```

### iOS
```bash
flutter build ios --release
```

## Features Implementation Status

- ✅ User Authentication (Login, Register, Forgot Password)
- ✅ Photo-based Question Solving
- ✅ Voice Question & Answer
- ✅ AI Notes Generation
- ✅ Quiz System with AI-generated questions
- ✅ Progress Tracking
- ✅ Study Timetable Planning
- ✅ Settings and Profile Management
- 🔄 Push Notifications (In Progress)
- 🔄 Offline Mode (In Progress)
- 🔄 Advanced Analytics (Planned)
- 🔄 Social Features (Planned)

## Monetization

### Free Plan
- 10 AI questions per day
- Basic quiz features
- Limited notes generation
- Ads between sessions

### Pro Plan (₹99/month)
- Unlimited AI questions
- Advanced study analytics
- Priority support
- Ad-free experience
- Custom study plans
- Offline mode

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

© 2024 Anuved Technologies. All rights reserved.

## Support

For support and queries:
- Email: <EMAIL>
- Phone: +91 12345 67890
- Website: www.aiguru.com

## Version History

### v1.0.0 (Current)
- Initial release
- Core AI features
- Basic user management
- Quiz and notes system
- Study planning tools

---

**AI Guru - Apka Study Sathi** - Your AI-powered study companion for academic success! 🚀📚